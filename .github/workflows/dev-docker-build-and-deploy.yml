name: "[DEV] Docker Build and Deploy"

on:
    push:
        branches: ["dev"]
    workflow_dispatch:
        inputs:
            version:
                description: "Version"
                required: true
                default: "latest"

env:
    REGISTRY: r-hub.thecapsai.com
    IMAGE_NAME: edtech-fe-dev
    DOCKER_COMPOSE_DIR: /home/<USER>/edtech-infra

jobs:
    build_and_push:
        name: Build and Push Docker Image
        environment: dev
        runs-on: ubuntu-latest
        permissions:
            contents: read
            packages: write

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3

            - name: Log into registry
              uses: docker/login-action@v3
              with:
                  registry: ${{ env.REGISTRY }}
                  username: ${{ secrets.DOCKER_USERNAME }}
                  password: ${{ secrets.DOCKER_PASSWORD }}

            - name: Extract Docker metadata
              id: meta
              uses: docker/metadata-action@v5
              with:
                  images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
                  tags: |
                      type=ref,event=branch
                      type=ref,event=pr
                      type=semver,pattern={{version}}
                      type=semver,pattern={{major}}.{{minor}}
                      type=sha,format=short
                      type=raw,value=latest

            - name: Build and push Docker image
              uses: docker/build-push-action@v5
              with:
                  context: .
                  file: apps/web/Dockerfile
                  push: ${{ github.event_name != 'pull_request' }}
                  tags: ${{ steps.meta.outputs.tags }}
                  labels: ${{ steps.meta.outputs.labels }}
                  cache-from: type=gha
                  cache-to: type=gha,mode=max
                  secrets: |
                      "content_api_key=${{ secrets.CONTENT_API_KEY }}"
                  build-args: |
                      NEXT_PUBLIC_API_URL=${{ vars.NEXT_PUBLIC_API_URL }}
                      NEXT_PUBLIC_GOOGLE_CLIENT_ID=${{ vars.NEXT_PUBLIC_GOOGLE_CLIENT_ID }}
                      NEXT_PUBLIC_GOOGLE_REDIRECT_URI=${{ vars.NEXT_PUBLIC_GOOGLE_REDIRECT_URI }}

    deploy:
        name: Deploy to droplet via SSH action
        environment: dev
        runs-on: ubuntu-latest
        needs: build_and_push

        steps:
            - name: Deploy to droplet via SSH action
              uses: appleboy/ssh-action@master
              with:
                  host: ${{ secrets.HOST }}
                  username: ${{ secrets.USERNAME }}
                  key: ${{ secrets.KEY }}
                  script: |
                      cd ${{ env.DOCKER_COMPOSE_DIR }}
                      # pull new images
                      docker compose pull edtech-fe
                      # stop old containers
                      docker compose stop edtech-fe
                      # remove old containers
                      docker compose rm -f edtech-fe
                      # start new containers
                      docker compose up -d --no-deps edtech-fe
                      # prune unused images
                      docker image prune -a -f
