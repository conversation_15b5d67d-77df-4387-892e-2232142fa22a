# EdTech Frontend

## 🚀 Features

- **Monorepo Architecture**: Organized workspace with Turborepo for efficient builds and development
- **shadcn/ui Integration**: Pre-configured shadcn/ui components with customizable variants
- **Shared UI Package**: Centralized component library (`@workspace/ui`) for consistent design across apps
- **TypeScript Support**: Full TypeScript configuration with shared configs across the workspace
- **Tailwind CSS**: Pre-configured with custom design tokens and dark mode support
- **ESLint & Prettier**: Shared linting and formatting configurations
- **Next.js Ready**: Web app configured with Next.js 15 and React 19
- **Theme Support**: Built-in dark/light theme switching with next-themes

## 📁 Project Structure

```
├── apps/
│   └── web/                 # Next.js web application
├── packages/
│   ├── ui/                  # Shared UI component library
│   ├── eslint-config/       # Shared ESLint configurations
│   └── typescript-config/   # Shared TypeScript configurations
├── package.json             # Root package.json with workspace configuration
└── turbo.json              # Turborepo configuration
```

- **`apps/web`**: Main Next.js application that consumes the shared UI components
- **`packages/ui`**: Contains all shadcn/ui components, styles, and utilities
- **`packages/eslint-config`**: Shared ESLint configurations for different project types
- **`packages/typescript-config`**: Shared TypeScript configurations for consistency

## 🛠 Prerequisites

- **Node.js**: Version 20 or higher
- **Bun**: Version 1.2.0 (specified as package manager)
- **Git**: For version control

## 🚀 Getting Started

1. **Clone or create from template**:

   ```bash
   # If using as a template, create your project first
   ```

2. **Install dependencies**:

   ```bash
   bun install
   ```

3. **Initialize shadcn/ui** (if not already done):

   ```bash
   bunx --bun shadcn@latest init
   ```

4. **Start development server**:

   ```bash
   bun dev
   ```

5. **Open your browser** and navigate to `http://localhost:3000`

## 📥 Adding Components

To add components to your app, run the following command at the root of your `web` app:

```bash
bunx --bun shadcn@latest add button -c apps/web
```

This will place the ui components in the `packages/ui/src/components` directory.

### Using Components

To use the components in your app, import them from the `ui` package:

```tsx
import { Button } from "@workspace/ui/components/button";
```

## 🧪 Testing

Testing is not yet configured in this template. You can add your preferred testing framework (Jest, Vitest, Playwright, etc.) as needed for your project requirements.

## 📜 Scripts

The following scripts are available in the root `package.json`:

- **`bun dev`**: Start development servers for all apps using Turborepo
- **`bun build`**: Build all apps and packages for production
- **`bun lint`**: Run ESLint across all packages and apps
- **`bun format`**: Format code using Prettier across TypeScript, TSX, and Markdown files

### App-specific Scripts (apps/web)

- **`bun dev`**: Start Next.js development server with Turbopack
- **`bun build`**: Build the Next.js application for production
- **`bun start`**: Start the production server
- **`bun lint`**: Run Next.js ESLint
- **`bun lint:fix`**: Run ESLint with auto-fix
- **`bun typecheck`**: Run TypeScript type checking

## 🎨 Tailwind

Your `tailwind.config.ts` and `globals.css` are already set up to use the components from the `ui` package. The configuration includes:

- Custom design tokens and CSS variables
- Dark mode support
- Shared styles across the monorepo
- Pre-configured component variants
