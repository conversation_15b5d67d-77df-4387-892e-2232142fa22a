# Build stage
FROM oven/bun:1-alpine AS builder

# Build time environment variables
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_GOOGLE_CLIENT_ID
ARG NEXT_PUBLIC_GOOGLE_REDIRECT_URI

ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_GOOGLE_CLIENT_ID=${NEXT_PUBLIC_GOOGLE_CLIENT_ID}
ENV NEXT_PUBLIC_GOOGLE_REDIRECT_URI=${NEXT_PUBLIC_GOOGLE_REDIRECT_URI}

# Set working directory
WORKDIR /app

# Copy workspace configuration
COPY package.json bun.lockb turbo.json ./
COPY apps/web/package.json ./apps/web/package.json
COPY packages/ ./packages/

# Copy source code
COPY apps/web/ ./apps/web/

# Install dependencies
RUN bun install

# Build the application
RUN --mount=type=secret,id=content_api_key \
    export CONTENT_API_KEY=$(cat /run/secrets/content_api_key) && \
    bun run build --filter=web

# Production stage
FROM oven/bun:1-alpine AS runner

# Set working directory
WORKDIR /app

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the standalone build output
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/standalone ./

# Copy static assets
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/.next/static ./apps/web/.next/static

# Copy public assets
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/public ./apps/web/public

# Set working directory to root for Next.js standalone
WORKDIR /app

# Switch to non-root user
USER nextjs

# Expose the port
EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

ENV PORT=3000
ENV HOSTNAME=0.0.0.0

# Start the application
CMD ["bun", "apps/web/server.js"]
