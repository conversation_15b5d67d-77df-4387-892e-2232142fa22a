"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import PageLoading from "@/components/layouts/page-loading";
import { LOGIN_ROUTE, PRIVATE_ROUTES } from "@/constants/routes";
import { useAuthStore } from "@/features/auth/stores/auth.store";
import { usePathname } from "@/i18n/navigation";

export default function PrivateLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const pathname = usePathname();
  const { checkAuth, isAuthenticated, isLoading } = useAuthStore();
  const [isInitialCheck, setIsInitialCheck] = useState(true);

  useEffect(() => {
    checkAuth();
    setIsInitialCheck(false);
  }, [checkAuth]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated && !PRIVATE_ROUTES.includes(pathname)) {
      // Preserve the current path for redirect after login
      const redirectPath =
        pathname !== LOGIN_ROUTE ? `?redirect=${encodeURIComponent(pathname)}` : "";
      router.replace(`${LOGIN_ROUTE}${redirectPath}`);
    }
  }, [isAuthenticated, isLoading, pathname, router]);

  if (isLoading || isInitialCheck || !isAuthenticated) {
    return <PageLoading />;
  }

  return <>{children}</>;
}
