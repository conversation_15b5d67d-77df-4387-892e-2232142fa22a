interface ClassroomExamsPageProps {
  params: Promise<{
    classroomCode: string;
    locale: string;
  }>;
}

export default async function ClassroomExamsPage({ params }: ClassroomExamsPageProps) {
  const { classroomCode } = await params;
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Exams</h1>
        <p className="text-muted-foreground">Manage Exams for classroom {classroomCode}</p>
      </div>

      <div className="rounded-lg border p-6">
        <h2 className="mb-4 text-lg font-semibold">Assignment List</h2>
        <p className="text-muted-foreground">
          This is where you would see and manage Exams for this classroom.
        </p>
      </div>
    </div>
  );
}
