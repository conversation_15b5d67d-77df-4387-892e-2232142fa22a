import TeacherClassroomHeader from "@/components/layouts/teachers/classrooms/teacher-classroom-header";
import TeacherClassroomNavbar from "@/components/layouts/teachers/classrooms/teacher-classroom-navbar";

export default function TeacherClassroomLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="[--header-height:calc(--spacing(auto))]">
      <div className="flex flex-col gap-2 border-b-2 p-5 pb-0">
        {/* Header */}
        <TeacherClassroomHeader />

        {/* Navbar */}
        <TeacherClassroomNavbar />
      </div>
      {/* Children */}
      <div className="p-5">{children}</div>
    </div>
  );
}
