interface ClassroomPageProps {
  params: Promise<{
    classroomId: string;
    locale: string;
  }>;
}

export default async function ClassroomPage({ params }: ClassroomPageProps) {
  const { classroomId } = await params;
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Classroom Overview</h1>
        <p className="text-muted-foreground">Welcome to classroom {classroomId}</p>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        <div className="rounded-lg border p-6">
          <h2 className="mb-2 text-lg font-semibold">Students</h2>
          <p className="text-2xl font-bold">24</p>
          <p className="text-muted-foreground text-sm">Enrolled students</p>
        </div>

        <div className="rounded-lg border p-6">
          <h2 className="mb-2 text-lg font-semibold">Assignments</h2>
          <p className="text-2xl font-bold">8</p>
          <p className="text-muted-foreground text-sm">Active assignments</p>
        </div>

        <div className="rounded-lg border p-6">
          <h2 className="mb-2 text-lg font-semibold">Average Grade</h2>
          <p className="text-2xl font-bold">85%</p>
          <p className="text-muted-foreground text-sm">Class average</p>
        </div>
      </div>

      <div className="rounded-lg border p-6">
        <h2 className="mb-4 text-lg font-semibold">Recent Activity</h2>
        <p className="text-muted-foreground">
          This is where you would see recent classroom activity and updates.
        </p>
      </div>
    </div>
  );
}
