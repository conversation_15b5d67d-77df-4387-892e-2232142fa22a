interface ClassroomSettingsPageProps {
  params: Promise<{
    classroomCode: string;
    locale: string;
  }>;
}

export default async function ClassroomSettingsPage({ params }: ClassroomSettingsPageProps) {
  const { classroomCode } = await params;
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Classroom Settings</h1>
        <p className="text-muted-foreground">Configure settings for classroom {classroomCode}</p>
      </div>

      <div className="rounded-lg border p-6">
        <h2 className="mb-4 text-lg font-semibold">General Settings</h2>
        <p className="text-muted-foreground">
          This is where you would configure classroom-specific settings.
        </p>
      </div>
    </div>
  );
}
