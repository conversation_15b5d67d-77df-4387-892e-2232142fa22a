interface ClassroomStudentsPageProps {
  params: Promise<{
    classroomCode: string;
    locale: string;
  }>;
}

export default async function ClassroomStudentsPage({ params }: ClassroomStudentsPageProps) {
  const { classroomCode } = await params;
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Students</h1>
        <p className="text-muted-foreground">Manage students in classroom {classroomCode}</p>
      </div>

      <div className="rounded-lg border p-6">
        <h2 className="mb-4 text-lg font-semibold">Student List</h2>
        <p className="text-muted-foreground">
          This is where you would see the list of students enrolled in this classroom.
        </p>
      </div>
    </div>
  );
}
