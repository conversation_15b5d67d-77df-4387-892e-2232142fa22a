"use client";

import BaseDialog from "@workspace/ui/components/base-dialog";
import { But<PERSON> } from "@workspace/ui/components/button";
import { PlusIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";

import {
  ClassroomCard,
  ClassroomCardSkeleton,
} from "@/features/teachers/classrooms/components/classroom-card";
import ClassroomCreateForm from "@/features/teachers/classrooms/components/classroom-create-form";
import { useGetClassrooms } from "@/features/teachers/classrooms/hooks/use-get-classrooms";

export default function ClassroomsPage() {
  const t = useTranslations();
  const [isOpenCreateDialog, setIsOpenCreateDialog] = useState(false);

  const { data, isLoading } = useGetClassrooms({
    page: 1,
    limit: 10,
  });

  return (
    <>
      <div className="space-y-6">
        <div className="flex justify-between">
          <h1 className="text-3xl font-bold">{t("teachers.classrooms.title")}</h1>
          <Button onClick={() => setIsOpenCreateDialog(true)}>
            <PlusIcon /> {t("common.create")}
          </Button>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {isLoading
            ? Array.from({ length: 10 }, (_, i) => <ClassroomCardSkeleton key={i} />)
            : data?.items.map((classroom) => (
                <ClassroomCard key={classroom.id} classroom={classroom} />
              ))}
        </div>
      </div>
      {isOpenCreateDialog && (
        <BaseDialog
          title={t("teachers.classrooms.create.title")}
          description={t("teachers.classrooms.create.description")}
          open={isOpenCreateDialog}
          onClose={() => setIsOpenCreateDialog(false)}
        >
          <ClassroomCreateForm />
        </BaseDialog>
      )}
    </>
  );
}
