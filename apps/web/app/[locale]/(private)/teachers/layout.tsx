"use client";

import { SidebarInset, SidebarProvider } from "@workspace/ui/components/sidebar";

import { TeacherHeader } from "@/components/layouts/teachers/teacher-header";
import { TeacherSidebar } from "@/components/layouts/teachers/teacher-sidebar";
import { usePathname } from "@/i18n/navigation";

export default function TeacherLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const pathname = usePathname();

  const isClassroomDetailLayout = pathname.startsWith("/teachers/classrooms/");
  const isExamDetailLayout = pathname.startsWith("/teachers/exams/");

  if (isClassroomDetailLayout || isExamDetailLayout) {
    return <>{children}</>;
  }

  return (
    <div className="[--header-height:calc(--spacing(14))]">
      <SidebarProvider className="flex flex-col">
        <TeacherHeader />
        <div className="flex flex-1">
          <TeacherSidebar />
          <SidebarInset className="p-5">{children}</SidebarInset>
        </div>
      </SidebarProvider>
    </div>
  );
}
