"use client";
import dynamic from "next/dynamic";
import { useState } from "react";

import { AnswerI, AnswerII, AnswerIII, AnswerIIICol } from "@/components/answer-sheet";
const AnswerSheet = dynamic(() => import("@/components/answer-sheet"), { ssr: false });

const ANSWERS_I: AnswerI[] = Array.from({ length: 40 }, () => {
  const answer = [false, false, false, false] as AnswerI;
  answer[Math.floor(Math.random() * 4)] = true;
  return answer;
});

const ANSWERS_II: AnswerII[] = Array.from({ length: 8 }, () => {
  const answer = [false, false, false, false] as AnswerII;
  answer[Math.floor(Math.random() * 4)] = true;
  return answer;
});

const ANSWERS_III: AnswerIII[] = Array.from({ length: 6 }, () => {
  return Array.from({ length: 4 }, () => {
    const answer = [
      false,
      false,
      false,
      false,
      false,
      false,
      false,
      false,
      false,
      false,
      false,
      false,
    ] as AnswerIIICol;
    answer[Math.floor(Math.random() * 12)] = true;
    return answer;
  }) as AnswerIII;
});

export default function TeachersPage() {
  const [answersI, setAnswersI] = useState<AnswerI[]>(ANSWERS_I);
  const [answersII, setAnswersII] = useState<AnswerII[]>(ANSWERS_II);
  const [answersIII, setAnswersIII] = useState<AnswerIII[]>(ANSWERS_III);

  return (
    <div>
      <div className="w-[1000px]">
        <AnswerSheet
          answersI={answersI}
          answersII={answersII}
          answersIII={answersIII}
          onAnswerChangeI={(answerIndex, answer) => {
            setAnswersI((prev) => {
              const newAnswers = [...prev];
              newAnswers[answerIndex] = answer;
              return newAnswers;
            });
          }}
          onAnswerChangeII={(answerIndex, answer) => {
            setAnswersII((prev) => {
              const newAnswers = [...prev];
              newAnswers[answerIndex] = answer;
              return newAnswers;
            });
          }}
          onAnswerChangeIII={(answerIndex, answer) => {
            setAnswersIII((prev) => {
              const newAnswers = [...prev];
              newAnswers[answerIndex] = answer;
              return newAnswers;
            });
          }}
        />
      </div>
    </div>
  );
}
