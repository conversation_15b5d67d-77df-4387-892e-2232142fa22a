"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { ArrowLeftIcon } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

import PageLoading from "@/components/layouts/page-loading";
import { useGetQuestion } from "@/features/teachers/questions/hooks/use-get-question";

export default function QuestionPage() {
  const params = useParams();
  const { id } = params;

  const { data: initialData, isLoading } = useGetQuestion(id?.toString() || "");

  if (isLoading) {
    return <PageLoading />;
  }

  if (!initialData) {
    return <div>Question not found.</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/teachers/questions">
            <Button variant="ghost" size="icon">
              <ArrowLeftIcon />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Question Detail</h1>
            <p className="text-muted-foreground">View and edit question details.</p>
          </div>
        </div>
      </div>

      {/* <QuestionForm initialData={initialData} /> */}
    </div>
  );
}
