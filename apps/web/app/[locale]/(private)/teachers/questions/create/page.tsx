import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { ArrowLeftIcon } from "lucide-react";
import Link from "next/link";

import { QuestionForm } from "@/features/teachers/questions/components/question-form";

export default function QuestionCreatePage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/teachers/questions">
            <Button variant="ghost" size="icon">
              <ArrowLeftIcon />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold">Create Question</h1>
            <p className="text-muted-foreground">Create a new question for your question bank.</p>
          </div>
        </div>
      </div>

      <QuestionForm />
    </div>
  );
}
