"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { DownloadIcon, PlusIcon, UploadIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

import QuestionsDataView from "@/features/teachers/questions/components/questions-data-view";

export default function QuestionsPage() {
  const router = useRouter();

  const handleCreateNew = () => {
    router.push("/teachers/questions/create");
  };

  const handleImport = () => {
    // TODO: Implement import functionality
    toast("Import questions");
  };

  const handleExport = () => {
    // TODO: Implement export functionality
    toast("Export questions");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Questions</h1>
          <p className="text-muted-foreground">
            Manage your question bank and create new questions for exams and assignments.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="secondaryOutline" onClick={handleImport}>
            <UploadIcon />
            Import
          </Button>
          <Button variant="secondaryOutline" onClick={handleExport}>
            <DownloadIcon />
            Export
          </Button>
          <Button onClick={handleCreateNew}>
            <PlusIcon />
            Create Question
          </Button>
        </div>
      </div>

      {/* Data View */}
      <QuestionsDataView />
    </div>
  );
}
