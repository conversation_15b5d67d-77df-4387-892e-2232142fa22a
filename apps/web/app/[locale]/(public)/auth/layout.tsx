"use client";

import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import GoogleOneTap from "@/components/google-one-tap";
import { LanguageSwitcher } from "@/components/language-switcher";
import PageLoading from "@/components/layouts/page-loading";
import { ThemeSwitcher } from "@/components/theme-swicher";
import { useAuthStore } from "@/features/auth/stores/auth.store";

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { checkAuth, isAuthenticated, isLoading } = useAuthStore();
  const [isInitialCheck, setIsInitialCheck] = useState(true);

  useEffect(() => {
    checkAuth();
    setIsInitialCheck(false);
  }, [checkAuth]);

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.replace("/");
    }
  }, [isLoading, isAuthenticated, router]);

  if (isLoading || isInitialCheck || isAuthenticated) {
    return <PageLoading />;
  }

  return (
    <div className="flex h-screen w-full">
      <GoogleOneTap />
      {/* Left image */}
      {/* <div
        className="hidden md:block w-1/2 h-screen bg-no-repeat bg-cover bg-center relative"
        style={{
          backgroundImage: "url('/images/minimal-home-desk-design.jpg')",
        }}
      > */}
      {/* Quotes */}
      {/* Logo */}
      {/* </div> */}

      {/* Right form */}
      <div className="h-screen w-full">
        <div className="relative flex h-screen w-full items-center justify-center p-5">
          <div className="absolute top-0 left-0 flex items-center space-x-2 p-5">
            <LanguageSwitcher />
            <ThemeSwitcher />
          </div>
          {children}
        </div>
      </div>
    </div>
  );
}
