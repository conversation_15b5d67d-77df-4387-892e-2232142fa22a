import "@workspace/ui/globals.css";

import { Metadata } from "next";
import { Quicksand } from "next/font/google";

import { Providers } from "@/components/providers";
import { siteConfig } from "@/config/site";

const fontSans = Quicksand({
  subsets: ["latin"],
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: siteConfig.name,
  description: siteConfig.description,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${fontSans.variable} font-sans antialiased`}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
