import { Button } from "@workspace/ui/components/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@workspace/ui/components/card";
import { Separator } from "@workspace/ui/components/separator";
import Link from "next/link";
import { getTranslations } from "next-intl/server";

export default async function NotFound() {
  const t = await getTranslations("notFound");
  return (
    <div className="from-background to-muted/20 flex min-h-svh items-center justify-center bg-gradient-to-br">
      <div className="container mx-auto max-w-4xl p-6">
        <div className="flex flex-col items-center justify-center gap-8">
          {/* 404 Icon */}
          <div className="relative flex items-center justify-center">
            <div className="text-muted-foreground/20 text-9xl font-bold select-none">4</div>
            <div className="">
              <svg
                width="120"
                height="120"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary"
              >
                <circle cx="11" cy="11" r="8" />
                <path d="m21 21-4.35-4.35" />
                <line x1="13" y1="9" x2="9" y2="13" />
                <line x1="9" y1="9" x2="13" y2="13" />
              </svg>
            </div>
            <div className="text-muted-foreground/20 text-9xl font-bold select-none">4</div>
          </div>

          {/* Main Content Card */}
          <Card className="w-full max-w-2xl shadow-lg">
            <CardHeader className="text-center">
              <CardTitle className="text-destructive text-4xl font-bold">{t("title")}</CardTitle>
              <CardDescription className="mt-2 text-lg">{t("subtitle")}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-muted-foreground text-center leading-relaxed">
                {t("description")}
              </p>

              <Separator />

              {/* Suggestions */}
              <div className="space-y-4">
                <h3 className="text-center text-lg font-semibold">{t("suggestions.title")}</h3>
                <div className="grid gap-3 text-sm">
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full">
                      <span className="text-primary text-xs font-semibold">1</span>
                    </div>
                    <p className="text-muted-foreground">{t("suggestions.checkUrl")}</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full">
                      <span className="text-primary text-xs font-semibold">2</span>
                    </div>
                    <p className="text-muted-foreground">{t("suggestions.goBack")}</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full">
                      <span className="text-primary text-xs font-semibold">3</span>
                    </div>
                    <p className="text-muted-foreground">{t("suggestions.visitHome")}</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full">
                      <span className="text-primary text-xs font-semibold">4</span>
                    </div>
                    <p className="text-muted-foreground">{t("suggestions.useSearch")}</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="bg-primary/10 mt-0.5 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full">
                      <span className="text-primary text-xs font-semibold">5</span>
                    </div>
                    <p className="text-muted-foreground">{t("suggestions.contactSupport")}</p>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Action Buttons */}
              <div className="flex flex-col justify-center gap-3 sm:flex-row">
                <Link href="/" className="flex-1">
                  <Button variant={"primary"} className="w-full">
                    {t("returnHome")}
                  </Button>
                </Link>
              </div>

              {/* Additional Help */}
              <div className="text-muted-foreground text-center text-sm">
                <p>
                  {t("helpText")}{" "}
                  <Link href="/contact" className="text-primary font-medium hover:underline">
                    {t("contactSupport")}
                  </Link>
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Fun Element */}
          <div className="text-center">
            <p className="text-muted-foreground text-sm">{t("funElement")}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
