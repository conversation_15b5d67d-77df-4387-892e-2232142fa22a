import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Chính Sách Thanh Toán",
};

export default function PaymentPolicyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-rose-50">
      <div className="container mx-auto max-w-4xl px-4 py-12">
        {/* Header Section */}
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold text-slate-900 lg:text-5xl">
            Chính Sách Thanh Toán
          </h1>
          <p className="text-lg text-slate-600">
            Xử lý thanh toán minh bạch và an toàn cho hành trình giáo dục của bạn.
          </p>
          <div className="mt-4 inline-flex items-center rounded-full bg-rose-100 px-4 py-2 text-sm text-rose-800">
            <span>Cập nhật lần cuối: {new Date().toLocaleDateString()}</span>
          </div>
        </div>

        {/* Table of Contents */}
        <div className="mb-12 rounded-xl bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold text-slate-900">Mục Lục</h2>
          <nav className="grid gap-2 md:grid-cols-2">
            {[
              "Chuyển Khoản Ngân Hàng",
              "Thông Tin Thanh Toán",
              "Hoàn Tiền & Hủy Bỏ",
              "Thanh Toán Thất Bại",
              "Bảo Mật Thanh Toán",
              "Thông Tin Liên Hệ",
            ].map((section, index) => (
              <a
                key={section}
                href={`#section-${index + 1}`}
                className="text-slate-600 transition-colors duration-200 hover:text-rose-600"
              >
                {index + 1}. {section}
              </a>
            ))}
          </nav>
        </div>

        {/* Content Sections */}
        <div className="space-y-8">
          {/* Section 1 */}
          <section id="section-1" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">1. Chuyển Khoản Ngân Hàng</h2>
            <div className="space-y-4 text-slate-700">
              <p>
                Chúng tôi chấp nhận chuyển khoản ngân hàng để đảm bảo an toàn và minh bạch trong quá
                trình thanh toán cho các dịch vụ giáo dục của bạn.
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="flex items-center space-x-3 rounded-lg border border-slate-200 p-4">
                  <div className="text-2xl">🏦</div>
                  <div>
                    <h4 className="font-semibold text-slate-900">Chuyển Khoản Ngân Hàng</h4>
                    <p className="text-sm text-slate-600">
                      Chuyển khoản trực tiếp từ tài khoản ngân hàng
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-3 rounded-lg border border-slate-200 p-4">
                  <div className="text-2xl">📋</div>
                  <div>
                    <h4 className="font-semibold text-slate-900">Thông Tin Tài Khoản</h4>
                    <p className="text-sm text-slate-600">
                      Thông tin tài khoản sẽ được cung cấp sau khi đặt hàng
                    </p>
                  </div>
                </div>
              </div>
              <div className="rounded-lg border-l-4 border-blue-400 bg-blue-50 p-4">
                <h3 className="mb-2 font-semibold text-blue-900">🔒 Bảo Mật Lên Hàng Đầu</h3>
                <p className="text-sm text-blue-800">
                  Tất cả các thanh toán được xử lý an toàn thông qua các bộ xử lý thanh toán đáng
                  tin cậy của chúng tôi sử dụng mã hóa và các biện pháp bảo mật tiêu chuẩn ngành.
                </p>
              </div>
            </div>
          </section>

          {/* Section 2 */}
          <section id="section-2" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">2. Thông Tin Thanh Toán</h2>
            <div className="space-y-4 text-slate-700">
              <p>
                Thông tin thanh toán chính xác là điều cần thiết để xử lý thanh toán của bạn và duy
                trì bảo mật tài khoản.
              </p>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-rose-500"></div>
                  <p>
                    <strong>Thông Tin Đầy Đủ:</strong> Bạn phải cung cấp thông tin mua hàng và tài
                    khoản hiện tại, đầy đủ và chính xác cho tất cả các giao dịch.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-rose-500"></div>
                  <p>
                    <strong>Cập Nhật Thông Tin:</strong> Bạn đồng ý cập nhật kịp thời thông tin tài
                    khoản và thanh toán để giữ cho nó luôn cập nhật và chính xác.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-rose-500"></div>
                  <p>
                    <strong>Xác Minh:</strong> Chúng tôi có thể xác minh thông tin thanh toán của
                    bạn trước khi xử lý giao dịch vì mục đích bảo mật.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-rose-500"></div>
                  <p>
                    <strong>Sử Dụng Được Ủy Quyền:</strong> Bạn xác nhận rằng bạn được ủy quyền sử
                    dụng phương thức thanh toán được cung cấp.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Section 3 */}
          <section id="section-3" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">3. Hoàn Tiền & Hủy Bỏ</h2>
            <div className="space-y-4 text-slate-700">
              <div className="rounded-lg border-l-4 border-green-400 bg-green-50 p-4">
                <h3 className="mb-2 font-semibold text-green-900">💰 Xử Lý Hoàn Tiền</h3>
                <p className="text-green-800">
                  Yêu cầu hoàn tiền sẽ được xử lý trong vòng 30 ngày kể từ khi được phê duyệt. Hoàn
                  tiền được phát hành đến phương thức thanh toán ban đầu được sử dụng để mua hàng.
                </p>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-slate-900">Điều Kiện Được Hoàn Tiền:</h4>
                <ul className="ml-6 list-disc space-y-2">
                  <li>Vấn đề kỹ thuật ngăn cản việc truy cập dịch vụ</li>
                  <li>Phí trùng lặp do nhầm lẫn</li>
                  <li>Dịch vụ không được cung cấp như mô tả</li>
                  <li>Hủy bỏ trong thời gian gia hạn (khác nhau theo dịch vụ)</li>
                </ul>
              </div>
              <div className="rounded-lg bg-slate-50 p-4">
                <h4 className="mb-2 font-semibold text-slate-900">Quy Trình Hủy Bỏ</h4>
                <p className="text-sm text-slate-600">
                  Để hủy đăng ký hoặc yêu cầu hoàn tiền, vui lòng liên hệ bộ phận thanh toán của
                  chúng tôi hoặc sử dụng tùy chọn hủy bỏ trong cài đặt tài khoản của bạn.
                </p>
              </div>
            </div>
          </section>

          {/* Section 4 */}
          <section id="section-4" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">4. Thanh Toán Thất Bại</h2>
            <div className="space-y-4 text-slate-700">
              <div className="rounded-lg border-l-4 border-red-400 bg-red-50 p-4">
                <h3 className="mb-2 font-semibold text-red-900">⚠️ Xử Lý Thanh Toán Thất Bại</h3>
                <p className="text-red-800">
                  Nếu thanh toán thất bại, chúng tôi sẽ cố gắng xử lý thanh toán lại bằng thông tin
                  thanh toán đã lưu. Nếu nhiều lần thử thất bại, tài khoản của bạn có thể bị tạm
                  ngừng cho đến khi thanh toán được giải quyết.
                </p>
              </div>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="rounded-lg bg-slate-50 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">Thử Lại Tự Động</h4>
                  <p className="text-sm text-slate-600">Tối đa 3 lần thử tự động trong 7 ngày</p>
                </div>
                <div className="rounded-lg bg-slate-50 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">Tạm Ngừng Tài Khoản</h4>
                  <p className="text-sm text-slate-600">
                    Tạm ngừng tạm thời sau khi thử lại thất bại
                  </p>
                </div>
                <div className="rounded-lg bg-slate-50 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">Khôi Phục Dịch Vụ</h4>
                  <p className="text-sm text-slate-600">
                    Quyền truy cập đầy đủ được khôi phục sau khi thanh toán được giải quyết
                  </p>
                </div>
                <div className="rounded-lg bg-slate-50 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">Liên Hệ Hỗ Trợ</h4>
                  <p className="text-sm text-slate-600">
                    Hỗ trợ 24/7 có sẵn cho các vấn đề thanh toán
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Section 5 */}
          <section id="section-5" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">5. Bảo Mật Thanh Toán</h2>
            <div className="space-y-4 text-slate-700">
              <p>
                Chúng tôi ưu tiên bảo mật thông tin thanh toán của bạn và sử dụng các biện pháp bảo
                mật hàng đầu ngành để bảo vệ dữ liệu của bạn.
              </p>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="rounded-lg bg-slate-50 p-4 text-center">
                  <div className="mb-3 text-3xl">🔐</div>
                  <h4 className="mb-2 font-semibold">Mã Hóa</h4>
                  <p className="text-sm text-slate-600">Mã hóa SSL/TLS cho tất cả giao dịch</p>
                </div>
                <div className="rounded-lg bg-slate-50 p-4 text-center">
                  <div className="mb-3 text-3xl">🛡️</div>
                  <h4 className="mb-2 font-semibold">Tuân Thủ PCI</h4>
                  <p className="text-sm text-slate-600">Tiêu chuẩn ngành thẻ thanh toán</p>
                </div>
                <div className="rounded-lg bg-slate-50 p-4 text-center">
                  <div className="mb-3 text-3xl">🔍</div>
                  <h4 className="mb-2 font-semibold">Phát Hiện Gian Lận</h4>
                  <p className="text-sm text-slate-600">Hệ thống ngăn chặn gian lận tiên tiến</p>
                </div>
              </div>
              <div className="rounded-lg border-l-4 border-green-400 bg-green-50 p-4">
                <p className="text-sm text-green-800">
                  <strong>🔒 Xử Lý An Toàn:</strong> Chúng tôi không bao giờ lưu trữ thông tin thanh
                  toán đầy đủ của bạn trên máy chủ của chúng tôi. Tất cả dữ liệu nhạy cảm được xử lý
                  bởi các bộ xử lý thanh toán được chứng nhận.
                </p>
              </div>
            </div>
          </section>

          {/* Section 6 */}
          <section id="section-6" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">6. Thông Tin Liên Hệ</h2>
            <div className="space-y-4 text-slate-700">
              <p>
                Đối với các câu hỏi liên quan đến thanh toán, câu hỏi thanh toán hoặc hỗ trợ với
                giao dịch, vui lòng liên hệ bộ phận thanh toán của chúng tôi:
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="rounded-lg bg-slate-50 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">📧 Hỗ Trợ Thanh Toán</h4>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-rose-600 transition-colors hover:text-rose-800"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="rounded-lg bg-slate-50 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">📍 Bộ Phận Thanh Toán</h4>
                  <p className="text-sm text-slate-600">
                    123 Đường Giáo Dục
                    <br />
                    Thành phố Công nghệ, TC 12345
                    <br />
                    Hoa Kỳ
                  </p>
                </div>
              </div>
              <div className="mt-6 rounded-lg bg-rose-50 p-4">
                <p className="text-sm text-rose-800">
                  <strong>Thời Gian Phản Hồi:</strong> Chúng tôi hướng đến việc trả lời tất cả các
                  câu hỏi liên quan đến thanh toán trong vòng 24-48 giờ trong các ngày làm việc.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
