import { Metadata } from "next";

export const metadata: Metadata = {
  title: "<PERSON><PERSON>h S<PERSON>ch <PERSON>ền Riêng <PERSON>",
};

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="container mx-auto max-w-4xl px-4 py-12">
        {/* Header Section */}
        <div className="mb-12 text-center">
          <h1 className="mb-4 text-4xl font-bold text-slate-900 lg:text-5xl">
            Ch<PERSON>h Sách Quyền Riêng Tư
          </h1>
          <p className="text-lg text-slate-600">
            Quyền riêng tư của bạn quan trọng với chúng tôi. Tìm hiểu cách chúng tôi thu thập, sử
            dụng và bảo vệ thông tin của bạn.
          </p>
          <div className="mt-4 inline-flex items-center rounded-full bg-blue-100 px-4 py-2 text-sm text-blue-800">
            <span>Cập nhật lần cuối: {new Date().toLocaleDateString()}</span>
          </div>
        </div>

        {/* Table of Contents */}
        <div className="mb-12 rounded-xl bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-xl font-semibold text-slate-900">Mục Lục</h2>
          <nav className="grid gap-2 md:grid-cols-2">
            {[
              "Thông Tin Chúng Tôi Thu Thập",
              "Cách Chúng Tôi Sử Dụng Thông Tin Của Bạn",
              "Chia Sẻ & Tiết Lộ Thông Tin",
              "Bảo Mật & Lưu Trữ Dữ Liệu",
              "Quyền & Lựa Chọn Của Bạn",
              "Quyền Riêng Tư Trẻ Em",
              "Thay Đổi Chính Sách Này",
              "Thông Tin Liên Hệ",
            ].map((section, index) => (
              <a
                key={section}
                href={`#section-${index + 1}`}
                className="text-slate-600 transition-colors duration-200 hover:text-blue-600"
              >
                {index + 1}. {section}
              </a>
            ))}
          </nav>
        </div>

        {/* Content Sections */}
        <div className="space-y-8">
          {/* Section 1 */}
          <section id="section-1" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">
              1. Thông Tin Chúng Tôi Thu Thập
            </h2>
            <div className="space-y-4 text-slate-700">
              <p>
                Chúng tôi thu thập thông tin bạn cung cấp trực tiếp cho chúng tôi, chẳng hạn như khi
                bạn tạo tài khoản, sử dụng dịch vụ của chúng tôi hoặc liên hệ với chúng tôi để được
                hỗ trợ. Điều này bao gồm:
              </p>
              <ul className="ml-6 list-disc space-y-2">
                <li>
                  <strong>Thông Tin Tài Khoản:</strong> Tên, địa chỉ email, mật khẩu và chi tiết hồ
                  sơ
                </li>
                <li>
                  <strong>Dữ Liệu Giáo Dục:</strong> Tiến độ khóa học, kết quả bài kiểm tra và sở
                  thích học tập
                </li>
                <li>
                  <strong>Dữ Liệu Giao Tiếp:</strong> Tin nhắn, phản hồi và yêu cầu hỗ trợ
                </li>
                <li>
                  <strong>Dữ Liệu Kỹ Thuật:</strong> Địa chỉ IP, loại trình duyệt, thông tin thiết
                  bị và phân tích sử dụng
                </li>
              </ul>
              <p>
                Chúng tôi cũng tự động thu thập một số thông tin nhất định khi bạn sử dụng nền tảng
                của chúng tôi, bao gồm các mẫu sử dụng, tương tác tính năng và chỉ số hiệu suất để
                cải thiện dịch vụ của chúng tôi.
              </p>
            </div>
          </section>

          {/* Section 2 */}
          <section id="section-2" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">
              2. Cách Chúng Tôi Sử Dụng Thông Tin Của Bạn
            </h2>
            <div className="space-y-4 text-slate-700">
              <p>Chúng tôi sử dụng thông tin chúng tôi thu thập cho các mục đích sau:</p>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="rounded-lg bg-blue-50 p-4">
                  <h3 className="mb-2 font-semibold text-blue-900">Cung Cấp Dịch Vụ</h3>
                  <p className="text-sm text-blue-800">
                    Để cung cấp, duy trì và cải thiện nền tảng giáo dục và dịch vụ của chúng tôi
                  </p>
                </div>
                <div className="rounded-lg bg-green-50 p-4">
                  <h3 className="mb-2 font-semibold text-green-900">Cá Nhân Hóa</h3>
                  <p className="text-sm text-green-800">
                    Để tùy chỉnh trải nghiệm học tập của bạn và đề xuất nội dung liên quan
                  </p>
                </div>
                <div className="rounded-lg bg-purple-50 p-4">
                  <h3 className="mb-2 font-semibold text-purple-900">Giao Tiếp</h3>
                  <p className="text-sm text-purple-800">
                    Để gửi cập nhật quan trọng, trả lời câu hỏi và cung cấp hỗ trợ khách hàng
                  </p>
                </div>
                <div className="rounded-lg bg-orange-50 p-4">
                  <h3 className="mb-2 font-semibold text-orange-900">Phân Tích</h3>
                  <p className="text-sm text-orange-800">
                    Để phân tích mẫu sử dụng và cải thiện hiệu suất và tính năng của nền tảng
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Section 3 */}
          <section id="section-3" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">
              3. Chia Sẻ & Tiết Lộ Thông Tin
            </h2>
            <div className="space-y-4 text-slate-700">
              <p className="font-medium text-green-700">
                ✅ Chúng tôi không bán, trao đổi hoặc chuyển giao thông tin cá nhân của bạn cho bên
                thứ ba mà không có sự đồng ý của bạn, ngoại trừ như mô tả dưới đây:
              </p>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
                  <p>
                    <strong>Nhà Cung Cấp Dịch Vụ:</strong> Chúng tôi có thể chia sẻ thông tin với
                    các nhà cung cấp dịch vụ bên thứ ba đáng tin cậy giúp chúng tôi vận hành nền
                    tảng và cung cấp dịch vụ cho bạn.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
                  <p>
                    <strong>Yêu Cầu Pháp Lý:</strong> Chúng tôi có thể tiết lộ thông tin nếu được
                    yêu cầu bởi pháp luật, lệnh tòa hoặc yêu cầu của chính phủ.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
                  <p>
                    <strong>Chuyển Nhượng Doanh Nghiệp:</strong> Trong trường hợp sáp nhập, mua lại
                    hoặc bán tài sản, thông tin người dùng có thể được chuyển giao như một phần của
                    giao dịch.
                  </p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="mt-1 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
                  <p>
                    <strong>Sự Đồng Ý:</strong> Với sự đồng ý rõ ràng của bạn, chúng tôi có thể chia
                    sẻ thông tin cho các mục đích cụ thể bạn đã phê duyệt.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Section 4 */}
          <section id="section-4" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">4. Bảo Mật & Lưu Trữ Dữ Liệu</h2>
            <div className="space-y-4 text-slate-700">
              <div className="rounded-lg border-l-4 border-yellow-400 bg-yellow-50 p-4">
                <h3 className="mb-2 font-semibold text-yellow-900">🔒 Biện Pháp Bảo Mật</h3>
                <p className="text-yellow-800">
                  Chúng tôi triển khai các biện pháp bảo mật tiêu chuẩn ngành bao gồm mã hóa, máy
                  chủ an toàn, kiểm tra bảo mật định kỳ và kiểm soát truy cập để bảo vệ thông tin cá
                  nhân của bạn.
                </p>
              </div>
              <div className="rounded-lg border-l-4 border-blue-400 bg-blue-50 p-4">
                <h3 className="mb-2 font-semibold text-blue-900">⏰ Lưu Trữ Dữ Liệu</h3>
                <p className="text-blue-800">
                  Chúng tôi chỉ lưu giữ thông tin cá nhân của bạn trong thời gian cần thiết cho các
                  mục đích được nêu trong chính sách này, trừ khi pháp luật yêu cầu thời gian lưu
                  giữ dài hơn. Bạn có thể yêu cầu xóa tài khoản và dữ liệu liên quan bất kỳ lúc nào.
                </p>
              </div>
            </div>
          </section>

          {/* Section 5 */}
          <section id="section-5" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">5. Quyền & Lựa Chọn Của Bạn</h2>
            <div className="space-y-4 text-slate-700">
              <p>Bạn có các quyền sau đối với thông tin cá nhân của mình:</p>
              <div className="grid gap-3 md:grid-cols-2">
                <div className="rounded-lg border border-slate-200 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">
                    Truy Cập & Khả Năng Chuyển Giao
                  </h4>
                  <p className="text-sm text-slate-600">
                    Yêu cầu bản sao dữ liệu cá nhân của bạn ở định dạng có thể chuyển giao
                  </p>
                </div>
                <div className="rounded-lg border border-slate-200 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">Sửa Đổi</h4>
                  <p className="text-sm text-slate-600">
                    Sửa chữa thông tin cá nhân không chính xác hoặc không đầy đủ
                  </p>
                </div>
                <div className="rounded-lg border border-slate-200 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">Xóa Bỏ</h4>
                  <p className="text-sm text-slate-600">
                    Yêu cầu xóa dữ liệu cá nhân của bạn (quyền được quên)
                  </p>
                </div>
                <div className="rounded-lg border border-slate-200 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">Hạn Chế</h4>
                  <p className="text-sm text-slate-600">
                    Hạn chế cách chúng tôi xử lý thông tin cá nhân của bạn
                  </p>
                </div>
              </div>
              <p className="mt-4 text-sm text-slate-500">
                Để thực hiện các quyền này, vui lòng liên hệ với chúng tôi bằng thông tin được cung
                cấp dưới đây.
              </p>
            </div>
          </section>

          {/* Section 6 */}
          <section id="section-6" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">6. Quyền Riêng Tư Trẻ Em</h2>
            <div className="space-y-4 text-slate-700">
              <p>
                Dịch vụ của chúng tôi được thiết kế cho mục đích giáo dục và có thể được sử dụng bởi
                các cá nhân dưới 18 tuổi dưới sự giám sát của phụ huynh hoặc người giám hộ. Chúng
                tôi không cố ý thu thập thông tin cá nhân từ trẻ em dưới 13 tuổi mà không có sự đồng
                ý của phụ huynh.
              </p>
              <div className="rounded-lg border-l-4 border-red-400 bg-red-50 p-4">
                <p className="text-sm text-red-800">
                  Nếu bạn là phụ huynh hoặc người giám hộ và tin rằng con bạn đã cung cấp thông tin
                  cá nhân cho chúng tôi mà không có sự đồng ý của bạn, vui lòng liên hệ với chúng
                  tôi ngay lập tức.
                </p>
              </div>
            </div>
          </section>

          {/* Section 7 */}
          <section id="section-7" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">7. Thay Đổi Chính Sách Này</h2>
            <div className="space-y-4 text-slate-700">
              <p>
                Chúng tôi có thể cập nhật Chính sách Quyền riêng tư này theo thời gian để phản ánh
                những thay đổi trong thực tiễn, công nghệ, yêu cầu pháp lý hoặc các yếu tố khác của
                chúng tôi.
              </p>
              <p>
                Chúng tôi sẽ thông báo cho bạn về bất kỳ thay đổi quan trọng nào bằng cách đăng
                chính sách cập nhật trên trang web của chúng tôi và cập nhật ngày &ldquo;Cập nhật
                lần cuối&rdquo;. Chúng tôi khuyến khích bạn xem lại chính sách này định kỳ.
              </p>
              <div className="rounded-lg bg-slate-50 p-4">
                <p className="text-sm text-slate-600">
                  Việc tiếp tục sử dụng dịch vụ của chúng tôi sau bất kỳ thay đổi nào đối với chính
                  sách này cấu thành việc chấp nhận các điều khoản cập nhật.
                </p>
              </div>
            </div>
          </section>

          {/* Section 8 */}
          <section id="section-8" className="rounded-xl bg-white p-8 shadow-sm">
            <h2 className="mb-4 text-2xl font-bold text-slate-900">8. Thông Tin Liên Hệ</h2>
            <div className="space-y-4 text-slate-700">
              <p>
                Nếu bạn có bất kỳ câu hỏi, thắc mắc hoặc yêu cầu nào liên quan đến Chính sách Quyền
                riêng tư này hoặc thực tiễn dữ liệu của chúng tôi, vui lòng liên hệ với chúng tôi:
              </p>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="rounded-lg bg-slate-50 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">📧 Email</h4>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-blue-600 transition-colors hover:text-blue-800"
                  >
                    <EMAIL>
                  </a>
                </div>
                <div className="rounded-lg bg-slate-50 p-4">
                  <h4 className="mb-2 font-semibold text-slate-900">📍 Địa Chỉ</h4>
                  <p className="text-sm text-slate-600">
                    123 Đường Giáo Dục
                    <br />
                    Thành phố Công nghệ, TC 12345
                    <br />
                    Hoa Kỳ
                  </p>
                </div>
              </div>
              <div className="mt-6 rounded-lg bg-blue-50 p-4">
                <p className="text-sm text-blue-800">
                  <strong>Thời Gian Phản Hồi:</strong> Chúng tôi hướng đến việc trả lời tất cả các
                  câu hỏi liên quan đến quyền riêng tư trong vòng 30 ngày kể từ khi nhận được.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
