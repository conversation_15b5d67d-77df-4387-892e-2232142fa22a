"use client";
import { cn } from "@workspace/ui/lib/utils";
import React from "react";

import { AnswerSheetBase } from "./answer-sheet-base";

export type AnswerI = [boolean, boolean, boolean, boolean];
export type AnswerII = [boolean, boolean, boolean, boolean];
export type AnswerIIICol = [
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
  boolean,
];
export type AnswerIII = [AnswerIIICol, AnswerIIICol, AnswerIIICol, AnswerIIICol];

type AnswerSheetProps = {
  answersI: AnswerI[];
  answersII: AnswerII[];
  answersIII: AnswerIII[];
  onAnswerChangeI: (answerIndex: number, answer: AnswerI) => void;
  onAnswerChangeII: (answerIndex: number, answer: AnswerII) => void;
  onAnswerChangeIII: (answerIndex: number, answer: AnswerIII) => void;
};

function AnswerSheet({
  answersI,
  answersII,
  answersIII,
  onAnswerChangeI,
  onAnswerChangeII,
  onAnswerChangeIII,
}: AnswerSheetProps) {
  return (
    <svg
      viewBox="0 0 3773 5334"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="h-full w-full"
    >
      <AnswerSheetBase />
      {Array.from({ length: 4 }, (_, block) =>
        Array.from({ length: 10 }, (_, row) => {
          const index = block * 10 + row;
          const answer = answersI[index];
          return Array.from({ length: 4 }, (_, column) => {
            const isAnswer = answer?.[column];
            return (
              <circle
                suppressHydrationWarning
                key={`sheet-1-${block}-${row}-${column}`}
                cx={522 + column * 155 + block * 780}
                cy={2030 + row * 82}
                r="30.5"
                stroke="#F36648"
                strokeWidth={4}
                className={cn(
                  "cursor-pointer hover:scale-120 hover:fill-gray-400",
                  isAnswer ? "fill-gray-400" : "fill-white",
                )}
                style={{
                  transformBox: "fill-box",
                  transformOrigin: "center",
                }}
                onClick={() => {
                  const newAnswers = [...answersI];
                  newAnswers[index] = [false, false, false, false];
                  newAnswers[index][column] = true;
                  onAnswerChangeI(index, newAnswers[index]);
                }}
              />
            );
          });
        }),
      )}

      {Array.from({ length: 4 }, (_, block) =>
        Array.from({ length: 2 }, (_, questionIndex) => {
          const anserIndex = questionIndex + block * 2;
          const answer = answersII[anserIndex];
          return Array.from({ length: 2 }, (_, column) => {
            return Array.from({ length: 4 }, (_, row) => {
              const isAnswer = answer?.[row] === (column === 0 ? true : false);
              return (
                <circle
                  suppressHydrationWarning
                  key={`sheet-2-${block}-${column}-${questionIndex}-${row}`}
                  cx={522 + column * 150 + block * 780 + questionIndex * 310}
                  cy={3200 + row * 78}
                  r="30.5"
                  stroke="#F36648"
                  strokeWidth={4}
                  fill="#fff"
                  className={cn(
                    "cursor-pointer hover:scale-120 hover:fill-gray-400",
                    isAnswer ? "fill-gray-400" : "fill-white",
                  )}
                  style={{
                    transformBox: "fill-box",
                    transformOrigin: "center",
                  }}
                  onClick={() => {
                    const newAnswers = [...answersII];
                    if (newAnswers[anserIndex]?.[row] !== undefined) {
                      newAnswers[anserIndex][row] = column === 0 ? true : false;
                      onAnswerChangeII(anserIndex, newAnswers[anserIndex]);
                    }
                  }}
                />
              );
            });
          });
        }),
      )}

      {Array.from({ length: 6 }, (_, block) => {
        return Array.from({ length: 4 }, (_, column) => {
          const answer = answersIII?.[block]?.[column];
          return Array.from({ length: 12 }, (_, row) => {
            const isAnswer = answer?.[row];
            console.log(block, column, row, isAnswer);
            return (
              <circle
                key={`sheet-3-${block}-${column}-${row}`}
                cx={518 + column * 97 + block * 495}
                cy={3905 + row * 83}
                r="30.5"
                stroke="#F36648"
                strokeWidth={4}
                className={cn(
                  "cursor-pointer hover:scale-120 hover:fill-gray-400",
                  isAnswer ? "fill-gray-400" : "fill-white",
                )}
                data-is-answer={isAnswer}
                data-index={`${block}-${column}-${row}`}
                style={{
                  transformBox: "fill-box",
                  transformOrigin: "center",
                }}
                onClick={() => {
                  const newAnswers = [...answersIII];
                  if (newAnswers[block]?.[column]) {
                    newAnswers[block][column] = Array(12).fill(false) as AnswerIIICol;
                    newAnswers[block][column][row] = true;
                    onAnswerChangeIII(block, newAnswers[block] as AnswerIII);
                  }
                }}
              />
            );
          });
        });
      })}
    </svg>
  );
}

export default AnswerSheet;
