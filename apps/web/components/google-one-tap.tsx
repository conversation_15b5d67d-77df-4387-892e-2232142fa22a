"use client";

import { useGoogleOneTapLogin } from "@react-oauth/google";
import toast from "react-hot-toast";

import { useGoogleTokenLogin } from "@/features/auth/hooks/use-google-login";
import { useAuthStore } from "@/features/auth/stores/auth.store";

function GoogleOneTap() {
  const { isAuthenticated, isLoading } = useAuthStore();
  const googleTokenLogin = useGoogleTokenLogin();

  useGoogleOneTapLogin({
    onSuccess: (credentialResponse) => {
      if (!credentialResponse.credential) {
        toast.error("Login Failed");
        return;
      }
      googleTokenLogin.mutate(credentialResponse.credential);
    },
    onError: () => {
      toast.error("Login Failed");
    },
    disabled: isLoading || isAuthenticated,
  });

  return null;
}

export default GoogleOneTap;
