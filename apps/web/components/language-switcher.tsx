"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select";
import { useLocale, useTranslations } from "next-intl";

import { usePathname, useRouter } from "@/i18n/navigation";
import { routing } from "@/i18n/routing";

export function LanguageSwitcher() {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();

  const handleLanguageChange = (newLocale: string) => {
    router.push(
      { pathname },
      {
        locale: newLocale,
      },
    );
  };

  const getLanguageLabel = (localeCode: string) => {
    switch (localeCode) {
      case "en":
        return t("language.english");
      case "vi":
        return t("language.vietnamese");
      default:
        return localeCode;
    }
  };

  const getLanguageIcon = (localeCode: string) => {
    switch (localeCode) {
      case "en":
        return "🇺🇸";
      case "vi":
        return "🇻🇳";
      default:
        return null;
    }
  };

  return (
    <div className="flex gap-2">
      <Select onValueChange={handleLanguageChange} value={locale}>
        <SelectTrigger
          className="hover:border-border !h-10 cursor-pointer border-2 border-transparent !bg-transparent p-2 shadow-none"
          showIcon={false}
        >
          <SelectValue placeholder={t("language.label")}>
            <span className="text-2xl"> {getLanguageIcon(locale)}</span>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {routing.locales.map((localeOption) => (
            <SelectItem key={localeOption} value={localeOption}>
              <span>{getLanguageIcon(localeOption)}</span>
              <span className="ml-2">{getLanguageLabel(localeOption)}</span>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
