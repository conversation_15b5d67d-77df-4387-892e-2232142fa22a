import { But<PERSON> } from "@workspace/ui/components/button";
import {
  FacebookIcon,
  GithubIcon,
  LinkedinIcon,
  MailIcon,
  MapPinIcon,
  PhoneIcon,
  TwitchIcon,
} from "lucide-react";
import Link from "next/link";
import { getTranslations } from "next-intl/server";

import Logo from "./logo";

export async function Footer() {
  const t = await getTranslations("footer");
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    platform: [
      { name: t("links.platform.teachers"), href: "/teachers" },
      { name: t("links.platform.students"), href: "/students" },
      { name: t("links.platform.classrooms"), href: "/teachers/classrooms" },
      { name: t("links.platform.questionBanks"), href: "/teachers/questions" },
    ],
    support: [
      { name: t("links.support.helpCenter"), href: "/help" },
      { name: t("links.support.contactUs"), href: "/contact" },
      { name: t("links.support.documentation"), href: "/docs" },
      { name: t("links.support.status"), href: "/status" },
    ],
    legal: [
      { name: t("links.legal.privacyPolicy"), href: "/p/privacy-policy" },
      { name: t("links.legal.termsOfService"), href: "/p/terms-of-service" },
      { name: t("links.legal.cookiePolicy"), href: "/p/cookie-policy" },
      { name: t("links.legal.paymentPolicy"), href: "/p/payment-policy" },
    ],
  };

  const socialLinks = [
    {
      name: "Twitter",
      href: "https://twitter.com/edtechplatform",
      icon: TwitchIcon,
    },
    {
      name: "GitHub",
      href: "https://github.com/edtechplatform",
      icon: GithubIcon,
    },
    {
      name: "LinkedIn",
      href: "https://linkedin.com/company/edtechplatform",
      icon: LinkedinIcon,
    },
    {
      name: "Facebook",
      href: "https://facebook.com/edtechplatform",
      icon: FacebookIcon,
    },
  ];

  return (
    <footer className="to-muted/20 from-muted border-t bg-gradient-to-b">
      <div className="container mx-auto px-4 py-12">
        {/* Main Footer Content */}
        <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex">
              <Logo />
            </div>
            <p className="text-muted-foreground max-w-xs text-sm leading-relaxed">
              {t("company.description")}
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <Link
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transform transition-colors duration-200 hover:scale-110"
                  aria-label={`Follow us on ${social.name}`}
                >
                  <social.icon className="h-5 w-5" />
                </Link>
              ))}
            </div>
          </div>

          {/* Platform Links */}
          <div className="space-y-4">
            <h3 className="text-foreground text-sm font-semibold">{t("sections.platform")}</h3>
            <ul className="space-y-2">
              {footerLinks.platform.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-muted-foreground hover:text-primary text-sm transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div className="space-y-4">
            <h3 className="text-foreground text-sm font-semibold">{t("sections.support")}</h3>
            <ul className="space-y-2">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-muted-foreground hover:text-primary text-sm transition-colors duration-200"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-foreground text-sm font-semibold">{t("sections.contact")}</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <MailIcon className="text-muted-foreground mt-0.5 h-4 w-4 flex-shrink-0" />
                <div className="text-muted-foreground text-sm">
                  <p className="text-foreground font-medium">Email</p>
                  <a
                    href="mailto:<EMAIL>"
                    className="hover:text-primary transition-colors duration-200"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <PhoneIcon className="text-muted-foreground mt-0.5 h-4 w-4 flex-shrink-0" />
                <div className="text-muted-foreground text-sm">
                  <p className="text-foreground font-medium">{t("contact.phone.label")}</p>
                  <a
                    href={`tel:${t("contact.phone.number").replace(/[^+\d]/g, "")}`}
                    className="hover:text-primary transition-colors duration-200"
                  >
                    {t("contact.phone.number")}
                  </a>
                </div>
              </div>
              <div className="flex items-start space-x-2">
                <MapPinIcon className="text-muted-foreground mt-0.5 h-4 w-4 flex-shrink-0" />
                <div className="text-muted-foreground text-sm">
                  <p className="text-foreground font-medium">{t("contact.address.label")}</p>
                  <p>{t("contact.address.location")}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="border-border/50 mb-8 border-t pt-8">
          <div className="mx-auto max-w-md text-center lg:mx-0 lg:flex lg:max-w-none lg:items-center lg:justify-between lg:text-left">
            <div className="lg:flex-1">
              <h3 className="text-foreground mb-2 text-lg font-semibold">
                {t("newsletter.title")}
              </h3>
              <p className="text-muted-foreground mb-4 text-sm lg:mb-0">
                {t("newsletter.description")}
              </p>
            </div>
            <div className="lg:ml-8 lg:flex-shrink-0">
              <div className="flex flex-col gap-3 sm:flex-row">
                <input
                  type="email"
                  placeholder={t("newsletter.emailPlaceholder")}
                  className="border-input bg-background focus:ring-primary flex-1 rounded-md border px-4 py-2 text-sm focus:border-transparent focus:ring-2 focus:outline-none"
                />
                <Button variant="primary">{t("newsletter.subscribeButton")}</Button>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-border/50 border-t pt-8">
          <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
            <div className="text-muted-foreground flex flex-col items-center gap-4 text-sm md:flex-row">
              <p>&copy; {currentYear} EdTech Platform. All rights reserved.</p>
              <div className="flex items-center space-x-4">
                {footerLinks.legal.map((link, index) => (
                  <span key={link.name} className="flex items-center">
                    {index > 0 && <span className="mx-2">•</span>}
                    <Link
                      href={link.href}
                      className="hover:text-primary transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
