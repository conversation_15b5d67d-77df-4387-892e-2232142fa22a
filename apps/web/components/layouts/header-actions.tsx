"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { useEffect } from "react";

import { useAuthStore } from "@/features/auth/stores/auth.store";

import { LanguageSwitcher } from "../language-switcher";
import { ThemeSwitcher } from "../theme-swicher";
import { UserDropdownMenu } from "../user-dropdown-menu";

export function HeaderActions() {
  const t = useTranslations();
  const { isLoading, isAuthenticated, checkAuth } = useAuthStore();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  if (isLoading)
    return (
      <div className="flex items-center space-x-2">
        <Skeleton className="h-10 w-10 rounded-full" />
      </div>
    );

  if (!isAuthenticated) {
    return (
      <Button variant="primary" size="sm">
        <Link href="/auth/login">{t("auth.login.title")}</Link>
      </Button>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <LanguageSwitcher />
      <ThemeSwitcher />
      <UserDropdownMenu />
    </div>
  );
}
