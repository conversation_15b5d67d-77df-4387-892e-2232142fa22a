import { cn } from "@workspace/ui/lib/utils";

import { HeaderActions } from "./header-actions";
import Logo from "./logo";

export function Header({ className, ...props }: React.HTMLAttributes<HTMLElement>) {
  return (
    <header className="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 w-full border-b-2 backdrop-blur">
      <div className={cn("flex h-14 items-center px-4", className)} {...props}>
        {/* Logo/Brand */}
        <Logo />

        {/* Right side actions */}
        <div className="ml-auto">
          <HeaderActions />
        </div>
      </div>
    </header>
  );
}
