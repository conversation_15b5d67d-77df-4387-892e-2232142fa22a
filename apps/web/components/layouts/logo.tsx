"use client";

import Link from "next/link";

import { usePathname } from "@/i18n/navigation";

const Logo = () => {
  const pathname = usePathname();

  const getRedirect = () => {
    let redirect = "";

    if (pathname.startsWith("/teachers")) {
      redirect = "/teachers";
    } else {
      redirect = "/";
    }

    return redirect;
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <Link href={getRedirect()} className="text-3xl font-bold">
        EdTech
      </Link>
    </div>
  );
};

export default Logo;
