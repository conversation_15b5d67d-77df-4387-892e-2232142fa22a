"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { Skeleton } from "@workspace/ui/components/skeleton";
import { LogOutIcon } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useTranslations } from "next-intl";

import { useGetClassroom } from "@/features/teachers/classrooms/hooks/use-get-classroom";

export default function TeacherClassroomHeader() {
  const t = useTranslations();
  const params = useParams();

  const { classroomCode } = params;

  const { data: classroom, isLoading } = useGetClassroom(classroomCode?.toString() || "");

  if (isLoading)
    return (
      <div className="h-[calc(var(--header-height))]">
        <Skeleton className="h-10 w-1/3" />
      </div>
    );

  return (
    <div className="flex h-[calc(var(--header-height))]">
      <h1 className="text-3xl font-bold">{classroom?.name}</h1>

      {/* Button exit classroom*/}
      <Link href="/teachers/classrooms" className="ml-auto" replace>
        <Button variant="destructiveOutline">
          <LogOutIcon className="h-6 w-6" />
          <span>{t("common.exit")}</span>
        </Button>
      </Link>
    </div>
  );
}
