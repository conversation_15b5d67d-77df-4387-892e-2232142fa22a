"use client";

import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from "@workspace/ui/components/navigation-menu";
import { cn } from "@workspace/ui/lib/utils";
import Link from "next/link";
import { useParams } from "next/navigation";

import { usePathname } from "@/i18n/navigation";

export default function TeacherClassroomNavbar() {
  const pathname = usePathname();
  const params = useParams();

  const { classroomCode } = params;

  const items = [
    {
      href: `/teachers/classrooms/${classroomCode}`,
      title: "Dashboard",
    },
    {
      href: `/teachers/classrooms/${classroomCode}/students`,
      title: "Students",
    },
    {
      href: `/teachers/classrooms/${classroomCode}/exams`,
      title: "Exams",
    },
    {
      href: `/teachers/classrooms/${classroomCode}/settings`,
      title: "Settings",
    },
  ];

  const isActive = (href: string) => {
    return pathname.includes(href);
  };

  return (
    <NavigationMenu className="w-full max-w-full justify-start">
      <NavigationMenuList className="space-x-8">
        {items.map((item) => (
          <NavigationMenuItem key={item.title}>
            <NavigationMenuLink
              className={cn(
                "group relative inline-flex h-11 w-max items-center justify-center px-0.5 py-3 font-semibold uppercase",
                "before:absolute before:inset-x-0 before:bottom-[-3px] before:h-[4px] before:scale-x-0 before:rounded-2xl before:bg-sky-500 before:transition-transform",
                "hover:text-accent-foreground hover:before:scale-x-100",
                "focus:text-accent-foreground focus:outline-hidden focus:before:scale-x-100",
                "disabled:pointer-events-none disabled:opacity-50",
                "hover:bg-transparent focus:bg-transparent active:bg-transparent",
              )}
              asChild
              active={isActive(item.href)}
            >
              <Link href={item.href} className="flex-row items-center gap-2.5">
                {item.title}
              </Link>
            </NavigationMenuLink>
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
    </NavigationMenu>
  );
}
