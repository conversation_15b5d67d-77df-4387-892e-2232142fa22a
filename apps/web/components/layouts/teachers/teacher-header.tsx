import { cn } from "@workspace/ui/lib/utils";

import { UserDropdownMenu } from "@/components/user-dropdown-menu";

import Logo from "../logo";
import { TeacherMenuButton } from "./teacher-menu-button";

export function TeacherHeader({ className, ...props }: React.HTMLAttributes<HTMLElement>) {
  return (
    <header className="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 w-full border-b-2 backdrop-blur">
      <div className={cn("flex h-14 items-center justify-between px-4", className)} {...props}>
        {/* Left side actions */}
        <TeacherMenuButton />

        {/* Logo/Brand */}
        <Logo />

        {/* Right side actions */}
        <UserDropdownMenu />
      </div>
    </header>
  );
}
