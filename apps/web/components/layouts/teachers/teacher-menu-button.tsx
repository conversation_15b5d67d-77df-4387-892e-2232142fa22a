"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { useSidebar } from "@workspace/ui/components/sidebar";
import { MenuIcon } from "lucide-react";

export function TeacherMenuButton() {
  const { isMobile, toggleSidebar } = useSidebar();

  if (!isMobile) return null;

  return (
    <Button variant="ghost" size="icon" onClick={toggleSidebar}>
      <MenuIcon className="h-6 w-6" />
      <span className="sr-only">Toggle menu</span>
    </Button>
  );
}
