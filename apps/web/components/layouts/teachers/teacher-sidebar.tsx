"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
} from "@workspace/ui/components/sidebar";
import { cn } from "@workspace/ui/lib/utils";
import Link from "next/link";

import { usePathname } from "@/i18n/navigation";

const items = [
  {
    href: "/teachers/#",
    title: "Dashboard",
  },
  {
    href: "/teachers/students",
    title: "Students",
  },
  {
    href: "/teachers/classrooms",
    title: "Classrooms",
  },
  {
    href: "/teachers/exams",
    title: "Exams",
  },
  {
    href: "/teachers/questions",
    title: "Questions",
  },
  {
    href: "/teachers/settings",
    title: "Settings",
  },
];

export function TeacherSidebar({ className, ...props }: React.HTMLAttributes<HTMLElement>) {
  const pathname = usePathname();

  const isActive = (href: string) => {
    return pathname.startsWith(href);
  };

  return (
    <Sidebar
      className={cn("top-(--header-height) h-[calc(100svh-var(--header-height))]!", className)}
      {...props}
    >
      <SidebarContent className="p-3">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="gap-3">
              {items.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <Button
                    asChild
                    className={cn("w-full justify-start")}
                    variant={isActive(item.href) ? "sidebarOutline" : "sidebar"}
                  >
                    <Link href={item.href}>
                      <span>{item.title}</span>
                    </Link>
                  </Button>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
