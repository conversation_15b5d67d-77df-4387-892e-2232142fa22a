"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@workspace/ui/components/dropdown-menu";
import { Skeleton } from "@workspace/ui/components/skeleton";
import Link from "next/link";
import { useTranslations } from "next-intl";
import toast from "react-hot-toast";

import { useAuthStore } from "@/features/auth/stores/auth.store";
import { UserAvatar } from "@/features/users/components/user-avatar";
import { usePathname, useRouter } from "@/i18n/navigation";

export function UserDropdownMenu() {
  const t = useTranslations();
  const router = useRouter();
  const pathname = usePathname();

  const { user, logout, isLoading } = useAuthStore();

  const handleLogout = () => {
    logout();
    toast.success(t("auth.logout.success"));
    router.replace("/");
  };

  const renderTeacherMenu = () => {
    if (user?.isTeacher && pathname.startsWith("/teachers")) {
      return (
        <>
          <DropdownMenuItem asChild>
            <Link href="/">{t("navigation.home")}</Link>
          </DropdownMenuItem>
        </>
      );
    }

    return (
      <>
        <DropdownMenuItem asChild>
          <Link href="/teachers">{t("navigation.dashboardTeacher")}</Link>
        </DropdownMenuItem>
      </>
    );
  };

  const renderModeratorMenu = () => {
    return <></>;
  };

  if (isLoading)
    return (
      <DropdownMenu>
        <DropdownMenuTrigger className="cursor-pointer rounded-full">
          <Skeleton className="h-10 w-10 rounded-full" />
        </DropdownMenuTrigger>
      </DropdownMenu>
    );

  if (!user) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="cursor-pointer rounded-full">
        <UserAvatar className="h-10 w-10" user={user} />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64 rounded-2xl">
        <DropdownMenuLabel className="flex flex-col items-center justify-center gap-2">
          <UserAvatar className="h-10 w-10" user={user} />
          <p className="truncate text-lg font-bold">{user.fullName}</p>
          <p className="text-muted-foreground truncate">{user.email}</p>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        {user.isTeacher && renderTeacherMenu()}

        {user.isModerator && renderModeratorMenu()}

        <DropdownMenuItem>
          <span>{t("settings.title")}</span>
        </DropdownMenuItem>

        <DropdownMenuItem onClick={handleLogout}>
          <span>{t("auth.logout.title")}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
