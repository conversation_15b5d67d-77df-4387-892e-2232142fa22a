"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import { Form } from "@workspace/ui/components/form";
import Loading from "@workspace/ui/components/loading";
import { RHFInput, RHFPasswordInput } from "@workspace/ui/components/rhf";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useLogin } from "@/features/auth/hooks/use-login";
import { createLoginSchema, LoginFormData } from "@/features/auth/schemas/auth.schema";
import { useSchemaTranslationMemo } from "@/hooks/use-schema-translation-memo";

export function LoginForm() {
  const t = useTranslations();
  const router = useRouter();

  const loginSchema = useSchemaTranslationMemo(createLoginSchema);
  const loginMutation = useLogin();

  const form = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleSubmit = async (data: LoginFormData) => {
    loginMutation.mutate(data, {
      onSuccess: () => {
        toast.success(t("auth.login.success"));
        router.replace("/");
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || t("auth.login.error"));
      },
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <RHFInput
          control={form.control}
          name="email"
          placeholder={t("auth.login.form.emailPlaceholder")}
          disabled={loginMutation.isPending}
        />

        <RHFPasswordInput
          control={form.control}
          name="password"
          placeholder={t("auth.login.form.passwordPlaceholder")}
          disabled={loginMutation.isPending}
        />

        <Button
          type="submit"
          variant="primary"
          className="w-full"
          disabled={loginMutation.isPending}
        >
          {loginMutation.isPending ? (
            <>
              <Loading />
              {t("auth.login.form.submittingButton")}
            </>
          ) : (
            t("auth.login.form.submitButton")
          )}
        </Button>
      </form>
    </Form>
  );
}
