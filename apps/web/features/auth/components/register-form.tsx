"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import { Form } from "@workspace/ui/components/form";
import Loading from "@workspace/ui/components/loading";
import { RHFInput, RHFPasswordInput } from "@workspace/ui/components/rhf";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useRegister } from "@/features/auth/hooks/use-register";
import { createRegisterSchema, RegisterFormData } from "@/features/auth/schemas/auth.schema";
import { useSchemaTranslationMemo } from "@/hooks/use-schema-translation-memo";

export function RegisterForm() {
  const t = useTranslations();
  const router = useRouter();

  const registerSchema = useSchemaTranslationMemo(createRegisterSchema);
  const registerMutation = useRegister();

  const form = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
  });

  const handleSubmit = async (data: RegisterFormData) => {
    registerMutation.mutate(data, {
      onSuccess: () => {
        toast.success(t("auth.register.success"));
        router.replace("/auth/login");
      },
      onError: (error) => {
        toast.error(error.response?.data?.message || t("auth.register.error"));
      },
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <RHFInput
          control={form.control}
          name="fullName"
          placeholder={t("auth.register.form.fullNamePlaceholder")}
          disabled={registerMutation.isPending}
        />

        <RHFInput
          control={form.control}
          name="email"
          placeholder={t("auth.register.form.emailPlaceholder")}
          type="email"
          disabled={registerMutation.isPending}
        />

        <RHFPasswordInput
          control={form.control}
          name="password"
          placeholder={t("auth.register.form.passwordPlaceholder")}
          disabled={registerMutation.isPending}
        />

        <RHFPasswordInput
          control={form.control}
          name="confirmPassword"
          placeholder={t("auth.register.form.confirmPasswordPlaceholder")}
          disabled={registerMutation.isPending}
        />

        <Button
          type="submit"
          variant="primary"
          className="w-full"
          disabled={registerMutation.isPending}
        >
          {registerMutation.isPending ? (
            <>
              <Loading />
              {t("auth.register.form.submittingButton")}
            </>
          ) : (
            t("auth.register.form.submitButton")
          )}
        </Button>
      </form>
    </Form>
  );
}
