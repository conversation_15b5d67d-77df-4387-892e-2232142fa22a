"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import { Icons } from "@workspace/ui/components/icons";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";

import { initiateGoogleLogin } from "../hooks/use-google-login";

export function SocialLoginOptions() {
  const t = useTranslations();
  const router = useRouter();

  const handleGoogleLogin = () => {
    initiateGoogleLogin();
  };

  return (
    <div className="grid grid-cols-3 gap-3">
      <Button variant="ghostOutline" onClick={handleGoogleLogin}>
        <Icons.google className="h-4 w-4" /> {t("auth.social.google")}
      </Button>
      <Button variant="ghostOutline" disabled>
        <Icons.facebook className="h-4 w-4" /> {t("auth.social.facebook")}
      </Button>
      <Button variant="ghostOutline" disabled>
        <Icons.apple className="h-4 w-4" /> {t("auth.social.apple")}
      </Button>
    </div>
  );
}
