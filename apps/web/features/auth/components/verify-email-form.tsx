"use client";

import { But<PERSON> } from "@workspace/ui/components/button";
import Loading from "@workspace/ui/components/loading";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { useEffect } from "react";
import toast from "react-hot-toast";

import { useVerifyEmail } from "@/features/auth/hooks/use-verify-email";

export function VerifyEmailForm() {
  const t = useTranslations();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");
  const verifyEmailMutation = useVerifyEmail();

  useEffect(() => {
    if (!token) return;

    verifyEmailMutation.mutate(token);
  }, [token, verifyEmailMutation]);

  const handleResendEmail = async () => {
    toast.error("Dev feature not implemented yet. Please contact support.");
  };

  if (verifyEmailMutation.isSuccess) {
    return (
      <div className="space-y-4 rounded-xl border-2 border-b-4 p-4 text-center">
        <p className="text-success">{t("auth.verifyEmail.success")}</p>
      </div>
    );
  }

  if (verifyEmailMutation.isError) {
    return (
      <div className="space-y-4 rounded-xl border-2 border-b-4 p-4 text-center">
        <p className="text-destructive">{t("auth.verifyEmail.error")}</p>
        <Button onClick={handleResendEmail}>{t("auth.verifyEmail.resendButton")}</Button>
      </div>
    );
  }

  if (verifyEmailMutation.isPending) {
    return (
      <div className="flex items-center justify-center">
        <Loading />
        <span className="ml-2">{t("auth.verifyEmail.verifying")}</span>
      </div>
    );
  }

  return (
    <div className="space-y-4 rounded-xl border-2 border-b-4 p-4 text-center">
      <p>{t("auth.verifyEmail.checkInbox")}</p>
      <p className="text-muted-foreground text-sm">{t("auth.verifyEmail.spamNote")}</p>
    </div>
  );
}
