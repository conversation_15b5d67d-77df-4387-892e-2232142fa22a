import { useMutation } from "@workspace/query";
import { AuthResponse } from "@workspace/types";

import { apiClient } from "@/lib/api-client";

import { useAuthStore } from "../stores/auth.store";

export function useGoogleLogin() {
  const { setAuth, setUser } = useAuthStore();

  return useMutation({
    mutationFn: (code: string) => {
      return apiClient.Auth.googleLogin(code);
    },
    onSuccess: async (data: AuthResponse) => {
      setAuth(data);

      try {
        const user = await apiClient.User.getMe();
        setUser(user);
      } catch (error) {
        console.error("Error checking auth:", error);
      }

      return data;
    },
  });
}

export function useGoogleTokenLogin() {
  const { setAuth, setUser } = useAuthStore();

  return useMutation({
    mutationFn: (credential: string) => {
      return apiClient.Auth.googleOneTapLogin(credential);
    },
    onSuccess: async (data: AuthResponse) => {
      setAuth(data);

      try {
        const user = await apiClient.User.getMe();
        setUser(user);
      } catch (error) {
        console.error("Error checking auth:", error);
      }

      return data;
    },
  });
}

export function initiateGoogleLogin() {
  const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
  const baseRedirectUri = process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI;

  if (!clientId || !baseRedirectUri) {
    console.error("Google OAuth environment variables not configured");
    return;
  }

  // Construct full redirect URI with locale
  const redirectUri = `${window.location.origin}/auth/oauth-callback`;

  const googleAuthUrl = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${clientId}&redirect_uri=${encodeURIComponent(
    redirectUri,
  )}&response_type=code&scope=email%20profile&state=google`;

  window.location.href = googleAuthUrl;
}
