import { useMutation } from "@workspace/query";
import { AuthResponse } from "@workspace/types";

import { apiClient } from "@/lib/api-client";

import { LoginFormData } from "../schemas/auth.schema";
import { useAuthStore } from "../stores/auth.store";

export function useLogin() {
  const { setAuth, setUser, clearLoginForm } = useAuthStore();

  return useMutation({
    mutationFn: (data: LoginFormData) => {
      return apiClient.Auth.login(data);
    },
    onSuccess: async (data: AuthResponse) => {
      setAuth(data);
      clearLoginForm();

      try {
        const user = await apiClient.User.getMe();
        setUser(user);
      } catch (error) {
        console.error("Error checking auth:", error);
      }

      return data;
    },
  });
}
