import { AuthResponse, User } from "@workspace/types";
import { create } from "zustand";

import { apiClient } from "@/lib/api-client";

interface AuthState {
  // State
  user: User | null;
  loginForm: {
    fullName: string;
    email: string;
  };
  isAuthenticated: boolean;
  isLoading: boolean;

  // Actions
  logout: () => void;
  checkAuth: () => Promise<void>;
  setAuth: (auth: AuthResponse) => void;
  setUser: (user: User) => void;
  setLoading: (loading: boolean) => void;
  setLoginForm: (loginForm: { fullName: string; email: string }) => void;
  clearLoginForm: () => void;
}

// Helper functions for localStorage
const setToken = (key: string, value: string) => {
  localStorage.setItem(key, value);
};

const getToken = (key: string): string | null => {
  return localStorage.getItem(key);
};

const removeToken = (key: string) => {
  localStorage.removeItem(key);
};

export const useAuthStore = create<AuthState>()((set) => ({
  // Initial state
  user: null,
  loginForm: {
    fullName: "",
    email: "",
  },
  isAuthenticated: false,
  isLoading: true,

  // Logout action
  logout: async () => {
    // Clear tokens from localStorage
    removeToken("accessToken");
    removeToken("refreshToken");
    // Reset state
    set({
      user: null,
      isAuthenticated: false,
      isLoading: false,
    });
  },

  // Set auth action
  setAuth: async (auth: AuthResponse) => {
    console.log("setAuth", auth);
    // Store token in localStorage
    setToken("accessToken", auth.accessToken);

    if (auth.refreshToken) {
      setToken("refreshToken", auth.refreshToken);
    }

    // Now we should be able to read the token
    console.log("setAuth done", getToken("accessToken"));

    set({
      isAuthenticated: true,
      isLoading: false,
    });
  },

  // Check authentication status
  checkAuth: async () => {
    set({ isLoading: true });

    const token = getToken("accessToken");

    try {
      if (!token) {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
        return;
      }

      // Get user profile
      const user = await apiClient.User.getMe();

      set({
        user: user,
        isAuthenticated: true,
      });
    } catch (error) {
      console.error("Auth check failed:", error);

      // Clear invalid tokens
      removeToken("accessToken");
      removeToken("refreshToken");

      set({
        user: null,
        isAuthenticated: false,
      });
    } finally {
      set({ isLoading: false });
    }
  },

  // Set user action (for manual updates)
  setUser: (user: User) => {
    set({ user, isAuthenticated: true });
  },

  // Set loading action
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // Set login form action
  setLoginForm: (loginForm: { fullName: string; email: string }) => {
    set({ loginForm });
  },

  // Clear login form action
  clearLoginForm: () => {
    set({ loginForm: { fullName: "", email: "" } });
  },
}));
