import { Classroom, ClassroomStatus } from "@workspace/types/domains";
import { AspectRatio } from "@workspace/ui/components/aspect-ratio";
import { Badge } from "@workspace/ui/components/badge";
import { Card, CardContent, CardDescription, CardHeader } from "@workspace/ui/components/card";
import { Skeleton } from "@workspace/ui/components/skeleton";
import Image from "next/image";
import Link from "next/link";

interface ClassroomCardProps {
  classroom: Classroom;
}

export function ClassroomCard({ classroom }: ClassroomCardProps) {
  const getStatus = () => {
    switch (classroom.status) {
      case ClassroomStatus.ACTIVE:
        return (
          <span className="flex items-center gap-2">
            <span className="relative flex h-3 w-3">
              <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-green-400 opacity-75"></span>
              <span className="relative inline-flex h-3 w-3 rounded-full bg-green-500"></span>
            </span>
          </span>
        );
      case ClassroomStatus.INACTIVE:
        return (
          <span className="flex items-center gap-2">
            <span className="relative flex h-3 w-3">
              <span className="absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
              <span className="relative inline-flex h-3 w-3 rounded-full bg-red-500"></span>
            </span>
          </span>
        );
    }
  };

  return (
    <Link href={`/teachers/classrooms/${classroom.code}`} className="block">
      <Card className="grid grid-cols-3 gap-5 p-5">
        <AspectRatio ratio={1 / 1}>
          <Image
            src={classroom.bannerImage || "/placeholder.png"}
            alt={classroom.name}
            width={0}
            height={0}
            sizes="100vw"
            className="col-span-1 h-full w-full rounded-lg border-2 object-cover"
          />
        </AspectRatio>

        <CardContent className="col-span-2 flex flex-col gap-2 p-0">
          <CardHeader className="p-0">
            <div className="flex w-full flex-col items-start justify-between gap-2">
              <div className="flex w-full items-center justify-between">
                <h2 className="flex-1 text-xl font-semibold">{classroom.name}</h2>
                {getStatus()}
              </div>
              <Badge variant="secondary">{classroom.code}</Badge>
            </div>
          </CardHeader>
          <CardDescription>{classroom.description}</CardDescription>
        </CardContent>
      </Card>
    </Link>
  );
}

export function ClassroomCardSkeleton() {
  return (
    <Card className="grid grid-cols-3 gap-5 p-5">
      <AspectRatio ratio={1 / 1}>
        <Skeleton className="col-span-1 h-full w-full rounded-lg border-2" />
      </AspectRatio>
      <CardContent className="col-span-2 flex flex-col gap-2 p-0">
        <CardHeader className="p-0">
          <div className="flex w-full flex-col items-start justify-between gap-2">
            <div className="flex w-full items-center justify-between">
              <Skeleton className="h-6 w-2/3" />
              <Skeleton className="h-3 w-3 rounded-full" />
            </div>
            <Skeleton className="h-5 w-20 rounded-full" />
          </div>
        </CardHeader>
        <Skeleton className="h-4 w-full" />
      </CardContent>
    </Card>
  );
}
