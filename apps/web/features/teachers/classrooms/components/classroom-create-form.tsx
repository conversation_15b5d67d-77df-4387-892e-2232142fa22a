"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { ClassroomType } from "@workspace/types/domains";
import { Button } from "@workspace/ui/components/button";
import { Form } from "@workspace/ui/components/form";
import Loading from "@workspace/ui/components/loading";
import { RHFCheckbox, RHFInput, RHFSelect, RHFTextarea } from "@workspace/ui/components/rhf";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";

import useCreateClassroom from "@/features/teachers/classrooms/hooks/use-create-classroom";
import {
  ClassroomCreateFormData,
  createCreateClassroomSchema,
} from "@/features/teachers/classrooms/schemas/classroom.schema";
import { useSchemaTranslationMemo } from "@/hooks/use-schema-translation-memo";

export default function ClassroomCreateForm() {
  const t = useTranslations();
  const createClassroomMutation = useCreateClassroom();

  const classroomSchema = useSchemaTranslationMemo(createCreateClassroomSchema);

  const form = useForm({
    resolver: zodResolver(classroomSchema),
    defaultValues: {
      name: "",
      description: "",
      type: ClassroomType.PUBLIC,
      showInWebsite: false,
      bannerImage: "",
    },
  });

  const handleSubmit = (data: ClassroomCreateFormData) => {
    createClassroomMutation.mutate(data);
  };

  const classroomTypeOptions = [
    { value: ClassroomType.PUBLIC, label: t("teachers.classrooms.type.public") },
    { value: ClassroomType.PRIVATE, label: t("teachers.classrooms.type.private") },
  ];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <RHFInput
          control={form.control}
          name="name"
          label={t("teachers.classrooms.fields.name")}
          placeholder={t("teachers.classrooms.placeholders.name")}
          disabled={createClassroomMutation.isPending}
        />

        <RHFTextarea
          control={form.control}
          name="description"
          label={t("teachers.classrooms.fields.description")}
          placeholder={t("teachers.classrooms.placeholders.description")}
          disabled={createClassroomMutation.isPending}
        />

        <RHFSelect
          control={form.control}
          name="type"
          label={t("teachers.classrooms.fields.type")}
          placeholder={t("teachers.classrooms.placeholders.type")}
          options={classroomTypeOptions}
          disabled={createClassroomMutation.isPending}
        />

        <RHFInput
          control={form.control}
          name="bannerImage"
          label={t("teachers.classrooms.fields.bannerImage")}
          placeholder={t("teachers.classrooms.placeholders.bannerImage")}
          disabled={createClassroomMutation.isPending}
        />

        <RHFCheckbox
          control={form.control}
          name="showInWebsite"
          label={t("teachers.classrooms.fields.showInWebsite")}
          disabled={createClassroomMutation.isPending}
        />

        <div className="flex justify-end space-x-3">
          <Button type="submit" variant="primary" disabled={createClassroomMutation.isPending}>
            {createClassroomMutation.isPending ? (
              <>
                <Loading />
                {t("common.creating")}
              </>
            ) : (
              t("common.create")
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
