import { useMutation } from "@workspace/query";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import toast from "react-hot-toast";

import { ClassroomCreateFormData } from "@/features/teachers/classrooms/schemas/classroom.schema";
import { apiClient } from "@/lib/api-client";
import { invalidateQueries } from "@/lib/queries";

const useCreateClassroom = () => {
  const t = useTranslations();
  const router = useRouter();

  const mutation = useMutation({
    mutationFn: (data: ClassroomCreateFormData) => apiClient.TeacherClassroom.createClassroom(data),
    onSuccess: () => {
      // Invalidate and refetch classrooms
      invalidateQueries.teachers.classrooms.all();

      toast.success(t("teachers.classrooms.create.success"));

      // Redirect to classrooms page
      router.push("/teachers/classrooms");
    },
    onError: (error: any) => {
      toast.error(error?.message || t("teachers.classrooms.create.error"));
    },
  });

  return mutation;
};

export default useCreateClassroom;
