import { useQuery } from "@workspace/query/hooks/use-query";
import { Classroom } from "@workspace/types/domains";

import { apiClient } from "@/lib/api-client";
import { queryKeys } from "@/lib/queries";

export const useGetClassroom = (idOrCode: string) => {
  return useQuery<Classroom>({
    queryKey: queryKeys.teachers.classrooms.detail(idOrCode),
    queryFn: () => apiClient.TeacherClassroom.getClassroom(idOrCode),
  });
};
