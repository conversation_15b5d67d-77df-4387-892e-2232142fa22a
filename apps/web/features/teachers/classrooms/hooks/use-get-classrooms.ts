import { useQuery } from "@workspace/query/hooks/use-query";
import { Classroom } from "@workspace/types/domains";
import { PaginationResponse, PaginationSearchRequest } from "@workspace/types/shared";

import { apiClient } from "@/lib/api-client";
import { queryKeys } from "@/lib/queries";

export const useGetClassrooms = (request: PaginationSearchRequest) => {
  return useQuery<PaginationResponse<Classroom[]>>({
    queryKey: queryKeys.teachers.classrooms.list(request),
    queryFn: () => apiClient.TeacherClassroom.getClassrooms(request),
  });
};
