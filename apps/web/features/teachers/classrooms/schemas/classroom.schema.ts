import { ClassroomType } from "@workspace/types/domains";
import { z } from "zod";

import { TranslationFunction } from "@/i18n/types/translation-i18n";

export function createCreateClassroomSchema(t: TranslationFunction) {
  return z.object({
    name: z.string().min(1, t("classroom.validation.nameRequired")),
    description: z.string().min(1, t("classroom.validation.descriptionRequired")),
    type: z.nativeEnum(ClassroomType, {
      error: t("classroom.validation.typeRequired"),
    }),
    showInWebsite: z.boolean().default(false),
    bannerImage: z.string().url(t("classroom.validation.bannerImageRequired")),
  });
}

// Export types
export type ClassroomCreateFormData = z.infer<ReturnType<typeof createCreateClassroomSchema>>;
