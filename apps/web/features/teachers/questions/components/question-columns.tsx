import { ColumnDef } from "@tanstack/react-table";
import { Question, QuestionType } from "@workspace/types/domains";
import { Badge } from "@workspace/ui/components/badge";
import { Button } from "@workspace/ui/components/button";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { formatDistanceToNow } from "date-fns";
import { ArrowUpDown, ArrowUpDownIcon, BookOpenIcon, TagIcon } from "lucide-react";

import {
  getDifficultyColor,
  getDifficultyLabel,
  getQuestionTypeLabel,
} from "../utils/question.util";
import { QuestionActionsCell } from "./question-cells";

export const columns: ColumnDef<Question>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => {
          table.toggleAllPageRowsSelected(!!value);
          //   if (value) {
          //     setSelectedQuestions(questions.map((q) => q.id));
          //   } else {
          //     setSelectedQuestions([]);
          //   }
        }}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => {
          row.toggleSelected(!!value);
          //   const questionId = row.original.id;
          //   if (value) {
          //     setSelectedQuestions((prev) => [...prev, questionId]);
          //   } else {
          //     setSelectedQuestions((prev) => prev.filter((id) => id !== questionId));
          //   }
        }}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "content",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium"
        >
          Question
          <ArrowUpDownIcon className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const content = row.getValue("content") as string;
      return (
        <div className="max-w-[300px]">
          <p className="truncate font-medium">{content}</p>
          {row.original.tags && row.original.tags.length > 0 && (
            <div className="mt-1 flex items-center gap-1">
              <TagIcon className="text-muted-foreground h-3 w-3" />
              <span className="text-muted-foreground text-xs">
                {row.original.tags.slice(0, 2).join(", ")}
                {row.original.tags.length > 2 && ` +${row.original.tags.length - 2}`}
              </span>
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "type",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium"
        >
          Type
          <ArrowUpDownIcon className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const type = row.getValue("type") as QuestionType;
      return (
        <Badge variant="outline" className="text-xs">
          {getQuestionTypeLabel(type)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "difficulty",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium"
        >
          Difficulty
          <ArrowUpDownIcon className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const difficulty = row.getValue("difficulty") as number;
      return (
        <Badge className={`text-xs ${getDifficultyColor(difficulty)}`}>
          {getDifficultyLabel(difficulty)}
        </Badge>
      );
    },
  },
  {
    accessorKey: "subject",
    header: "Subject",
    cell: ({ row }) => {
      const subject = row.original.subject;
      return subject ? (
        <div className="flex items-center gap-1">
          <BookOpenIcon className="text-muted-foreground h-3 w-3" />
          <span className="text-sm">{subject.name}</span>
        </div>
      ) : (
        <span className="text-muted-foreground text-sm">No subject</span>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => {
      return (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          className="h-auto p-0 font-medium"
        >
          Created
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      const createdAt = row.getValue("createdAt") as string;
      return (
        <span className="text-muted-foreground text-sm">
          {formatDistanceToNow(new Date(createdAt), { addSuffix: true })}
        </span>
      );
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const question = row.original;

      return <QuestionActionsCell question={question} />;
    },
  },
];
