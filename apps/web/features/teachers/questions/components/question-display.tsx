"use client";

import { Question, QuestionType } from "@workspace/types";
import { Badge } from "@workspace/ui/components/badge";
import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@workspace/ui/components/card";
import { Separator } from "@workspace/ui/components/separator";
import { formatDistanceToNow } from "date-fns";
import {
  BookOpen,
  CheckCircle,
  Clock,
  Copy,
  Edit,
  Eye,
  Hash,
  Tag,
  Trash2,
  User,
  XCircle,
} from "lucide-react";

interface QuestionDisplayProps {
  question: Question;
  onEdit?: () => void;
  onDuplicate?: () => void;
  onDelete?: () => void;
  onView?: () => void;
  showActions?: boolean;
  compact?: boolean;
}

export function QuestionDisplay({
  question,
  onEdit,
  onDuplicate,
  onDelete,
  onView,
  showActions = true,
  compact = false,
}: QuestionDisplayProps) {
  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return "bg-green-100 text-green-800 border-green-200";
      case 2:
        return "bg-blue-100 text-blue-800 border-blue-200";
      case 3:
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case 4:
        return "bg-orange-100 text-orange-800 border-orange-200";
      case 5:
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getDifficultyLabel = (difficulty: number) => {
    switch (difficulty) {
      case 1:
        return "Very Easy";
      case 2:
        return "Easy";
      case 3:
        return "Medium";
      case 4:
        return "Hard";
      case 5:
        return "Very Hard";
      default:
        return "Unknown";
    }
  };

  const getQuestionTypeLabel = (type: QuestionType) => {
    switch (type) {
      case QuestionType.MULTIPLE_CHOICE:
        return "Multiple Choice";
      case QuestionType.TRUE_FALSE_GROUP:
        return "True/False Group";
      case QuestionType.NUMBER_INPUT:
        return "Number Input";
      case QuestionType.TEXT_INPUT:
        return "Text Input";
      default:
        return "Unknown";
    }
  };

  const renderQuestionPreview = () => {
    // Type guard functions for better type safety
    const isMultipleChoiceQuestion = (
      q: Question,
    ): q is Question & {
      options: { choices: string[] };
      correctAnswer: { value: number | number[] };
    } => {
      return q.type === QuestionType.MULTIPLE_CHOICE;
    };

    const isTrueFalseGroupQuestion = (
      q: Question,
    ): q is Question & {
      options: { statements: string[] };
      correctAnswer: { value: boolean[] };
    } => {
      return q.type === QuestionType.TRUE_FALSE_GROUP;
    };

    const isNumberInputQuestion = (
      q: Question,
    ): q is Question & {
      options: { min?: number; max?: number; precision?: number };
      correctAnswer: { value: number };
    } => {
      return q.type === QuestionType.NUMBER_INPUT;
    };

    const isTextInputQuestion = (
      q: Question,
    ): q is Question & {
      options: { multiline?: boolean; maxLength?: number };
      correctAnswer: { value: string };
    } => {
      return q.type === QuestionType.TEXT_INPUT;
    };

    if (isMultipleChoiceQuestion(question)) {
      return (
        <div className="space-y-2">
          {question.options.choices.map((choice, index) => {
            const correctValue = question.correctAnswer.value;
            // Properly narrow the type before using includes
            const isCorrect = Array.isArray(correctValue)
              ? (correctValue as number[]).includes(index)
              : correctValue === index;

            return (
              <div key={index} className="flex items-center space-x-2 text-sm">
                {isCorrect ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-gray-400" />
                )}
                <span className={isCorrect ? "font-medium text-green-700" : "text-gray-600"}>
                  {choice || `Choice ${index + 1}`}
                </span>
              </div>
            );
          })}
        </div>
      );
    }

    if (isTrueFalseGroupQuestion(question)) {
      return (
        <div className="space-y-2">
          {question.options.statements.map((statement, index) => {
            const isTrue = question.correctAnswer.value[index];
            return (
              <div key={index} className="flex items-center space-x-2 text-sm">
                {isTrue ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <span className="text-gray-600">{statement || `Statement ${index + 1}`}</span>
                <Badge variant={isTrue ? "default" : "secondary"} className="text-xs">
                  {isTrue ? "True" : "False"}
                </Badge>
              </div>
            );
          })}
        </div>
      );
    }

    if (isNumberInputQuestion(question)) {
      return (
        <div className="text-sm">
          <span className="text-gray-600">Correct answer: </span>
          <span className="font-medium">{question.correctAnswer.value}</span>
          <div className="mt-1 text-xs text-gray-500">
            {question.options.min !== undefined && `Min: ${question.options.min}`}
            {question.options.min !== undefined && question.options.max !== undefined && " • "}
            {question.options.max !== undefined && `Max: ${question.options.max}`}
            {question.options.precision !== undefined &&
              ` • Precision: ${question.options.precision} decimal places`}
          </div>
        </div>
      );
    }

    if (isTextInputQuestion(question)) {
      return (
        <div className="text-sm">
          <span className="text-gray-600">Correct answer: </span>
          <span className="font-medium">"{question.correctAnswer.value}"</span>
          <div className="mt-1 text-xs text-gray-500">
            {question.options.multiline ? "Multi-line text" : "Single-line text"}
            {question.options.maxLength &&
              ` • Max length: ${question.options.maxLength} characters`}
          </div>
        </div>
      );
    }

    return <span className="text-sm text-gray-500">Preview not available</span>;
  };

  if (compact) {
    return (
      <Card className="transition-shadow hover:shadow-md">
        <CardContent className="p-4">
          <div className="flex items-start justify-between">
            <div className="min-w-0 flex-1">
              <p className="truncate text-sm font-medium text-gray-900">{question.content}</p>
              <div className="mt-1 flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {getQuestionTypeLabel(question.type)}
                </Badge>
                <Badge className={`text-xs ${getDifficultyColor(question.difficulty)}`}>
                  {getDifficultyLabel(question.difficulty)}
                </Badge>
              </div>
            </div>
            {showActions && (
              <div className="ml-2 flex items-center space-x-1">
                {onView && (
                  <Button variant="ghost" size="sm" onClick={onView}>
                    <Eye className="h-4 w-4" />
                  </Button>
                )}
                {onEdit && (
                  <Button variant="ghost" size="sm" onClick={onEdit}>
                    <Edit className="h-4 w-4" />
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-shadow hover:shadow-md">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{question.content}</CardTitle>
            <div className="mt-2 flex items-center space-x-2">
              <Badge variant="outline">{getQuestionTypeLabel(question.type)}</Badge>
              <Badge className={getDifficultyColor(question.difficulty)}>
                {getDifficultyLabel(question.difficulty)}
              </Badge>
              {question.subject && (
                <Badge variant="secondary">
                  <BookOpen className="mr-1 h-3 w-3" />
                  {question.subject.name}
                </Badge>
              )}
            </div>
          </div>
          {showActions && (
            <div className="flex items-center space-x-1">
              {onView && (
                <Button variant="ghost" size="sm" onClick={onView}>
                  <Eye className="h-4 w-4" />
                </Button>
              )}
              {onEdit && (
                <Button variant="ghost" size="sm" onClick={onEdit}>
                  <Edit className="h-4 w-4" />
                </Button>
              )}
              {onDuplicate && (
                <Button variant="ghost" size="sm" onClick={onDuplicate}>
                  <Copy className="h-4 w-4" />
                </Button>
              )}
              {onDelete && (
                <Button variant="ghost" size="sm" onClick={onDelete}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Question Preview */}
          <div>
            <h4 className="mb-2 text-sm font-medium">Answer Options:</h4>
            {renderQuestionPreview()}
          </div>

          {/* Explanation */}
          {question.explanation && (
            <div>
              <h4 className="mb-1 text-sm font-medium">Explanation:</h4>
              <p className="text-sm text-gray-600">{question.explanation}</p>
            </div>
          )}

          <Separator />

          {/* Metadata */}
          <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
            <div className="space-y-2">
              {question.user && (
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">Created by: {question.user.fullName}</span>
                </div>
              )}
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-400" />
                <span className="text-gray-600">
                  Created {formatDistanceToNow(new Date(question.createdAt), { addSuffix: true })}
                </span>
              </div>
              {question.topics && question.topics.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Hash className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-600">
                    Topics: {question.topics.map((topic) => topic.name).join(", ")}
                  </span>
                </div>
              )}
            </div>
            <div className="space-y-2">
              {question.tags && question.tags.length > 0 && (
                <div>
                  <div className="mb-1 flex items-center space-x-2">
                    <Tag className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-600">Tags:</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {question.tags.map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
