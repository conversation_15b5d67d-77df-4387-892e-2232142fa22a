"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { QuestionType, QuestionVisibility } from "@workspace/types";
import { Badge } from "@workspace/ui/components/badge";
import { But<PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { Form, FormDescription, FormLabel } from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import Loading from "@workspace/ui/components/loading";
import { RHFRadioGroup, RHFSelect, RHFSwitch, RHFTextarea } from "@workspace/ui/components/rhf";
import {
  ALargeSmallIcon,
  CircleCheckIcon,
  Plus,
  Tally4Icon,
  ToggleLeftIcon,
  X,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

import { useCreateQuestion } from "../hooks/use-create-question";
import {
  QuestionCreateInput,
  questionCreateSchema,
  QuestionUpdateInput,
} from "../schemas/question.schema";
import { MultipleChoiceOptions } from "./question-options/multiple-choice-options";
import { NumberInputOptions } from "./question-options/number-input-options";
import { TextInputOptions } from "./question-options/text-input-options";
import { TrueFalseGroupOptions } from "./question-options/true-false-group-options";

interface QuestionFormProps {
  initialData?: Partial<QuestionCreateInput | QuestionUpdateInput>;
}

const questionTypeOptions = [
  {
    value: QuestionType.MULTIPLE_CHOICE,
    label: "Multiple Choice",
    icon: <CircleCheckIcon className="h-4 w-4" />,
  },
  {
    value: QuestionType.TRUE_FALSE_GROUP,
    label: "True/False Group",
    icon: <ToggleLeftIcon className="h-4 w-4" />,
  },
  {
    value: QuestionType.NUMBER_INPUT,
    label: "Number Input",
    icon: <Tally4Icon className="h-4 w-4" />,
  },
  {
    value: QuestionType.TEXT_INPUT,
    label: "Text Input",
    icon: <ALargeSmallIcon className="h-4 w-4" />,
  },
];

export function QuestionForm({ initialData }: QuestionFormProps) {
  const router = useRouter();
  const createQuestionMutation = useCreateQuestion();

  const [tags, setTags] = useState<string[]>(initialData?.tags || []);
  const [newTag, setNewTag] = useState("");

  const form = useForm<QuestionCreateInput>({
    resolver: zodResolver(questionCreateSchema) as any,
    defaultValues: {
      content: "",
      type: QuestionType.MULTIPLE_CHOICE,
      visibility: QuestionVisibility.PRIVATE,
      explanation: "",
      difficulty: 3,
      tags: [],
      subjectId: "",
      topicIds: [],
      options: {
        choices: ["", ""],
        multipleAnswers: false,
      },
      ...initialData,
    },
  });

  const questionType = form.watch("type");

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      const updatedTags = [...tags, newTag.trim()];
      setTags(updatedTags);
      form.setValue("tags", updatedTags);
      setNewTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter((tag) => tag !== tagToRemove);
    setTags(updatedTags);
    form.setValue("tags", updatedTags);
  };

  const handleSubmit = (data: QuestionCreateInput) => {
    createQuestionMutation.mutate(data, {
      onSuccess: () => {
        toast.success("Question created successfully!");
        router.push("/teachers/questions");
      },
      onError: (error) => {
        toast.error(error?.message || "Failed to create question. Please try again.");
      },
    });
  };

  const renderQuestionOptions = () => {
    switch (questionType) {
      case QuestionType.MULTIPLE_CHOICE:
        return <MultipleChoiceOptions form={form} />;
      case QuestionType.TRUE_FALSE_GROUP:
        return <TrueFalseGroupOptions form={form} />;
      case QuestionType.NUMBER_INPUT:
        return <NumberInputOptions form={form} />;
      case QuestionType.TEXT_INPUT:
        return <TextInputOptions form={form} />;
      default:
        return null;
    }
  };

  useEffect(() => {
    console.log("watch", form.watch());
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <div className="flex grid-cols-12 flex-col-reverse gap-6 md:grid">
          <Card className="col-span-4">
            <CardHeader>
              <CardTitle>Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <RHFSwitch control={form.control} name="visibility" label="Public" />

              <RHFSelect
                control={form.control}
                name="difficulty"
                label="Difficulty Level *"
                options={[
                  {
                    value: "1",
                    label: "1 - Very Easy",
                  },
                  {
                    value: "2",
                    label: "2 - Easy",
                  },
                  {
                    value: "3",
                    label: "3 - Medium",
                  },
                  {
                    value: "4",
                    label: "4 - Hard",
                  },
                  {
                    value: "5",
                    label: "5 - Very Hard",
                  },
                ]}
              />

              {/* Tags */}
              <div className="space-y-3">
                <FormLabel>Tags</FormLabel>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add a tag..."
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                  />
                  <Button type="button" variant="secondaryOutline" onClick={handleAddTag}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                {tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="text-muted-foreground hover:text-foreground h-auto p-0"
                          onClick={() => handleRemoveTag(tag)}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                )}
                <FormDescription>
                  Add tags to help organize and search your questions.
                </FormDescription>
              </div>
            </CardContent>
          </Card>

          <div className="col-span-8 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Question Type *</CardTitle>
              </CardHeader>
              <CardContent>
                <RHFRadioGroup control={form.control} name="type" options={questionTypeOptions} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Question Content</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Question Content */}
                <RHFTextarea
                  control={form.control}
                  name="content"
                  label="Question Content *"
                  description="Write a clear and concise question. You can use markdown formatting."
                />

                {/* Question Type */}

                {/* Question Options and Correct Answer */}
                {renderQuestionOptions()}

                {/* Explanation */}
                <RHFTextarea
                  control={form.control}
                  name="explanation"
                  label="Explanation (Optional)"
                  description="This explanation will be shown to students after they answer the question."
                />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Action Buttons */}
        <Button type="submit" disabled={createQuestionMutation.isPending}>
          {createQuestionMutation.isPending && <Loading />}
          Create Question
        </Button>
      </form>
    </Form>
  );
}
