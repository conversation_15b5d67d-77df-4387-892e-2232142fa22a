"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@workspace/ui/components/card";
import { Checkbox } from "@workspace/ui/components/checkbox";
import { FormDescription, FormLabel, FormMessage } from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { RadioGroup, RadioGroupItem } from "@workspace/ui/components/radio-group";
import { RHFSwitch } from "@workspace/ui/components/rhf";
import { Plus, Trash2 } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

import { multipleChoiceOptionsSchema, QuestionCreateInput } from "../../schemas/question.schema";

interface MultipleChoiceOptionsProps {
  form: UseFormReturn<QuestionCreateInput>;
}

export function MultipleChoiceOptions({ form }: MultipleChoiceOptionsProps) {
  const options = form.watch("options") as z.infer<typeof multipleChoiceOptionsSchema>;
  const correctAnswer = form.watch("correctAnswer");

  const choices = options.choices;
  const multipleAnswers = options.multipleAnswers;
  const selectedAnswers = correctAnswer?.value as number[] | number; // Align with CorrectAnswerDto

  const addChoice = () => {
    const newChoices = [...choices, ""];
    form.setValue("options", {
      ...options,
      choices: newChoices,
    });
  };

  const removeChoice = (index: number) => {
    if (choices.length <= 2) return; // Minimum 2 choices

    const newChoices = choices.filter((_, i) => i !== index);
    form.setValue("options", {
      ...options,
      choices: newChoices,
    });

    // Update correct answer if necessary
    if (multipleAnswers && Array.isArray(selectedAnswers)) {
      const newSelectedAnswers = selectedAnswers
        .filter((answerIndex) => answerIndex !== index)
        .map((answerIndex) => (answerIndex > index ? answerIndex - 1 : answerIndex));

      form.setValue("correctAnswer", {
        value: newSelectedAnswers,
      });
    } else if (!multipleAnswers && typeof selectedAnswers === "number") {
      if (selectedAnswers === index) {
        // Reset to first choice if the selected choice was removed
        form.setValue("correctAnswer", {
          value: 0,
        });
      } else if (selectedAnswers > index) {
        // Adjust index if a choice before the selected one was removed
        form.setValue("correctAnswer", {
          value: selectedAnswers - 1,
        });
      }
    }
  };

  const updateChoice = (index: number, value: string) => {
    const newChoices = [...choices];
    newChoices[index] = value;
    form.setValue("options", {
      ...options,
      choices: newChoices,
    });
  };

  const toggleMultipleAnswers = (checked: boolean) => {
    form.setValue("options", {
      ...options,
      multipleAnswers: checked,
    });

    // Reset correct answer when switching modes
    if (checked) {
      // Switch to multiple answers mode
      const currentAnswer = typeof selectedAnswers === "number" ? [selectedAnswers] : [];
      form.setValue("correctAnswer", {
        value: currentAnswer,
      });
    } else {
      // Switch to single answer mode
      const currentAnswer =
        Array.isArray(selectedAnswers) && selectedAnswers.length > 0 ? selectedAnswers[0] : 0;
      form.setValue("correctAnswer", {
        value: currentAnswer || 0,
      });
    }
  };

  const handleSingleAnswerChange = (value: string) => {
    form.setValue("correctAnswer", {
      value: parseInt(value),
    });
  };

  const handleMultipleAnswerChange = (index: number, checked: boolean) => {
    const currentAnswers = Array.isArray(selectedAnswers) ? selectedAnswers : [];
    let newAnswers: number[];

    if (checked) {
      newAnswers = [...currentAnswers, index].sort((a, b) => a - b);
    } else {
      newAnswers = currentAnswers.filter((answerIndex) => answerIndex !== index);
    }

    form.setValue("correctAnswer", {
      value: newAnswers,
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Multiple Choice Options</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Multiple Answers Toggle */}
        <RHFSwitch
          control={form.control}
          name="options.multipleAnswers"
          label="Allow multiple correct answers"
          description="Enable this if students can select more than one correct answer."
        />

        {/* Answer Choices */}
        <div className="space-y-3">
          <FormLabel>Answer Choices *</FormLabel>

          {multipleAnswers ? (
            // Multiple answers mode - use checkboxes
            <div className="space-y-2">
              {choices.map((choice, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Checkbox
                    checked={Array.isArray(selectedAnswers) && selectedAnswers.includes(index)}
                    onCheckedChange={(checked) =>
                      handleMultipleAnswerChange(index, checked as boolean)
                    }
                  />
                  <Input
                    placeholder={`Choice ${index + 1}`}
                    value={choice}
                    onChange={(e) => updateChoice(index, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="secondaryOutline"
                    size="sm"
                    onClick={() => removeChoice(index)}
                    disabled={choices.length <= 2}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            // Single answer mode - use radio buttons
            <RadioGroup
              value={typeof selectedAnswers === "number" ? String(selectedAnswers) : "0"}
              onValueChange={handleSingleAnswerChange}
            >
              {choices.map((choice, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <RadioGroupItem value={String(index)} id={`choice-${index}`} />
                  <Label htmlFor={`choice-${index}`} className="sr-only">
                    Choice {index + 1}
                  </Label>
                  <Input
                    placeholder={`Choice ${index + 1}`}
                    value={choice}
                    onChange={(e) => updateChoice(index, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="secondaryOutline"
                    size="sm"
                    onClick={() => removeChoice(index)}
                    disabled={choices.length <= 2}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </RadioGroup>
          )}

          <Button
            type="button"
            variant="secondaryOutline"
            onClick={addChoice}
            disabled={choices.length >= 4}
            className="w-full"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Choice {choices.length < 4 ? `(${choices.length}/6)` : "(Maximum reached)"}
          </Button>

          <FormDescription>
            {multipleAnswers
              ? "Check all correct answers. Students will need to select all correct options."
              : "Select the single correct answer. Students will choose one option."}
          </FormDescription>
        </div>

        <FormMessage />
      </CardContent>
    </Card>
  );
}
