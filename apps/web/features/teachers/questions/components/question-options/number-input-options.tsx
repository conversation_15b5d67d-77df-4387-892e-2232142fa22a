"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@workspace/ui/components/card";
import { FormDescription } from "@workspace/ui/components/form";
import { RHFInput } from "@workspace/ui/components/rhf";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

import { numberInputOptionsSchema, QuestionCreateInput } from "../../schemas/question.schema";

interface NumberInputOptionsProps {
  form: UseFormReturn<QuestionCreateInput>;
}

export function NumberInputOptions({ form }: NumberInputOptionsProps) {
  const options = form.watch("options") as z.infer<typeof numberInputOptionsSchema>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Number Input Options</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormDescription>
          Configure the number input constraints and provide the correct answer.
        </FormDescription>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* Minimum Value */}
          <RHFInput
            control={form.control}
            name="options.min"
            label="Minimum Value"
            description="Optional minimum allowed value"
            type="number"
          />

          {/* Maximum Value */}
          <RHFInput
            control={form.control}
            name="options.max"
            label="Maximum Value"
            description="Optional maximum allowed value"
            type="number"
          />

          {/* Precision */}
          <RHFInput
            control={form.control}
            name="options.precision"
            label="Decimal Places"
            description="Number of decimal places (0-10)"
            type="number"
            min={0}
            max={10}
          />
        </div>

        {/* Correct Answer */}
        <RHFInput
          control={form.control}
          name="correctAnswer.value"
          label="Correct Answer *"
          description="The exact numerical answer that students should provide."
          type="number"
          min={options.min}
          max={options.max}
          step={options.precision !== undefined ? Math.pow(10, -options.precision) : "any"}
        />
      </CardContent>
    </Card>
  );
}
