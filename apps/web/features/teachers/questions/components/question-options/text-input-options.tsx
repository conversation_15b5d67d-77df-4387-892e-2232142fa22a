"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@workspace/ui/components/card";
import { FormDescription } from "@workspace/ui/components/form";
import { RHFInput, RHFSwitch, RHFTextarea } from "@workspace/ui/components/rhf";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

import { QuestionCreateInput, textInputOptionsSchema } from "../../schemas/question.schema";

interface TextInputOptionsProps {
  form: UseFormReturn<QuestionCreateInput>;
}

export function TextInputOptions({ form }: TextInputOptionsProps) {
  const options = form.watch("options") as z.infer<typeof textInputOptionsSchema>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Text Input Options</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormDescription>
          Configure the text input constraints and provide the correct answer.
        </FormDescription>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Maximum Length */}
          <RHFInput
            control={form.control}
            name="options.maxLength"
            label="Maximum Length"
            description="Maximum number of characters (1-5000)"
            type="number"
            min={1}
            max={5000}
          />

          {/* Multiline */}
          <RHFSwitch
            control={form.control}
            name="options.multiline"
            label="Allow multiple lines"
            description="Enable this for essay-style questions"
          />
        </div>

        {/* Correct Answer */}
        {options.multiline ? (
          <RHFTextarea
            control={form.control}
            name="correctAnswer.value"
            label="Correct Answer *"
            description="The exact text answer that students should provide."
          />
        ) : (
          <RHFInput
            control={form.control}
            name="correctAnswer.value"
            label="Correct Answer *"
            description="The exact text answer that students should provide."
          />
        )}
      </CardContent>
    </Card>
  );
}
