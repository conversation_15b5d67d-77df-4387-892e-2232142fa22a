"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@workspace/ui/components/card";
import { FormDescription, FormLabel, FormMessage } from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { RadioGroup, RadioGroupItem } from "@workspace/ui/components/radio-group";
import { Plus, Trash2 } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

import { QuestionCreateInput, trueFalseGroupOptionsSchema } from "../../schemas/question.schema";

interface TrueFalseGroupOptionsProps {
  form: UseFormReturn<QuestionCreateInput>;
}

export function TrueFalseGroupOptions({ form }: TrueFalseGroupOptionsProps) {
  const options = form.watch("options") as z.infer<typeof trueFalseGroupOptionsSchema>;
  const correctAnswer = form.watch("correctAnswer");

  const statements = options?.statements || [];
  const answers = (correctAnswer?.value as boolean[]) || [];

  const addStatement = () => {
    const newStatements = [...statements, ""];
    const newAnswers = [...answers, false];

    form.setValue("options", {
      ...options,
      statements: newStatements,
    });

    form.setValue("correctAnswer", {
      value: newAnswers,
    });
  };

  const removeStatement = (index: number) => {
    if (statements.length <= 1) return; // Minimum 1 statement

    const newStatements = statements.filter((_, i) => i !== index);
    const newAnswers = answers.filter((_, i) => i !== index);

    form.setValue("options", {
      ...options,
      statements: newStatements,
    });

    form.setValue("correctAnswer", {
      value: newAnswers,
    });
  };

  const updateStatement = (index: number, value: string) => {
    const newStatements = [...statements];
    newStatements[index] = value;

    form.setValue("options", {
      ...options,
      statements: newStatements,
    });
  };

  const updateAnswer = (index: number, value: boolean) => {
    const newAnswers = [...answers];
    newAnswers[index] = value;

    form.setValue("correctAnswer", {
      value: newAnswers,
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>True/False Group Options</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <FormDescription>
          Create a list of statements that students will mark as true or false.
        </FormDescription>

        {/* Statements */}
        <div className="space-y-3">
          <FormLabel>Statements *</FormLabel>

          <div className="space-y-4">
            {statements?.map((statement, index) => (
              <div key={index} className="space-y-2 rounded-lg border p-4">
                <div className="flex items-center space-x-2">
                  <span className="text-muted-foreground min-w-[80px] text-sm font-medium">
                    Statement {index + 1}:
                  </span>
                  <Input
                    placeholder={`Enter statement ${index + 1}...`}
                    value={statement}
                    onChange={(e) => updateStatement(index, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="secondaryOutline"
                    size="sm"
                    onClick={() => removeStatement(index)}
                    disabled={statements.length <= 1}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="ml-[100px] flex items-center gap-5">
                  <FormLabel className="text-sm">Correct Answer:</FormLabel>
                  <RadioGroup
                    value={answers[index]?.toString()} // Convert boolean to string for RadioGroup
                    onValueChange={(value) => updateAnswer(index, value === "true")}
                    className="mt-1 flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="true" id={`true-${index}`} />
                      <Label htmlFor={`true-${index}`} className="text-sm">
                        True
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="false" id={`false-${index}`} />
                      <Label htmlFor={`false-${index}`} className="text-sm">
                        False
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            ))}
          </div>

          <Button
            type="button"
            variant="secondaryOutline"
            onClick={addStatement}
            disabled={statements.length >= 4}
            className="w-full"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Statement {statements.length < 4 ? `(${statements.length}/4)` : "(Maximum reached)"}
          </Button>

          <FormDescription>
            Each statement should be clear and unambiguous. Students will mark each statement as
            true or false.
          </FormDescription>
        </div>

        <FormMessage />
      </CardContent>
    </Card>
  );
}
