import { Button } from "@workspace/ui/components/button";
import { Input } from "@workspace/ui/components/input";
import { SearchIcon, XIcon } from "lucide-react";

import { QuestionFilterInput } from "../schemas/question.schema";

interface QuestionToobarProps {
  filters: QuestionFilterInput;
  onFiltersChange: (filters: QuestionFilterInput) => void;
  onReset: () => void;
  onRefresh?: () => void;
}

const QuestionToobar = ({ filters, onFiltersChange, onReset, onRefresh }: QuestionToobarProps) => {
  const hasActiveFilters = Object.values(filters).some((value) => value !== undefined);

  const updateFilter = <K extends keyof QuestionFilterInput>(
    key: K,
    value: QuestionFilterInput[K],
  ) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  return (
    <div className="flex items-center gap-2">
      {/* Filter */}
      <div className="relative">
        <SearchIcon className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
        <Input
          placeholder="Search questions..."
          value={filters.search || ""}
          onChange={(e) => updateFilter("search", e.target.value || undefined)}
          className="h-11 rounded-xl border-2 pl-10"
        />
      </div>
      {/* Reset button */}

      {hasActiveFilters && (
        <Button onClick={onReset}>
          Reset <XIcon />
        </Button>
      )}
    </div>
  );
};

export default QuestionToobar;
