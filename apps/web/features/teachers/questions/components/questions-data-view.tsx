import { Button } from "@workspace/ui/components/button";
import { DataTable } from "@workspace/ui/components/data-table";
import { GridIcon, ListIcon } from "lucide-react";
import { useState } from "react";

import { useGetQuestions } from "../hooks/use-get-questions";
import { QuestionFilterInput } from "../schemas/question.schema";
import { columns } from "./question-columns";
import QuestionToobar from "./question-toolbar";
import QuestionsGridView from "./questions-grid-view";

const QuestionsDataView = () => {
  const [viewMode, setViewMode] = useState<"table" | "grid">("table");

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [filters, setFilters] = useState<QuestionFilterInput>({});

  const {
    data: products,
    isLoading,
    refetch,
  } = useGetQuestions({
    page: currentPage,
    limit: itemsPerPage,
    ...filters,
  });

  const handleFiltersReset = () => {
    setFilters({});
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        {/* Filters */}
        <QuestionToobar
          filters={filters}
          onFiltersChange={setFilters}
          onReset={handleFiltersReset}
        />

        {/* Mode Toggle & Pagination */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4"></div>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "table" ? "primary" : "default"}
              size="sm"
              onClick={() => setViewMode("table")}
            >
              <ListIcon />
            </Button>
            <Button
              variant={viewMode === "grid" ? "primary" : "default"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <GridIcon />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      {viewMode === "table" ? (
        <DataTable
          columns={columns}
          data={products?.items || []}
          pagination={products?.meta}
          onPageChange={(page) => {
            setCurrentPage(page);
            refetch();
          }}
          onItemsPerPageChange={(itemsPerPage) => {
            setItemsPerPage(itemsPerPage);
            refetch();
          }}
          isLoading={isLoading}
        />
      ) : (
        <QuestionsGridView
          questions={products?.items || []}
          pagination={products?.meta}
          onPageChange={(page) => {
            setCurrentPage(page);
            refetch();
          }}
          onItemsPerPageChange={(itemsPerPage) => {
            setItemsPerPage(itemsPerPage);
            refetch();
          }}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default QuestionsDataView;
