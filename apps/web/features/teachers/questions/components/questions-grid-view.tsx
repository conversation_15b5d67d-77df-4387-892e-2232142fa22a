import { Question } from "@workspace/types/domains";
import { Card, CardContent } from "@workspace/ui/components/card";
import { DataTablePagination } from "@workspace/ui/components/data-table";
import { FileTextIcon } from "lucide-react";

import { QuestionDisplay } from "./question-display";

interface QuestionsGridViewProps {
  questions: Question[];
  isLoading: boolean;
  pagination?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
}

const QuestionsGridView = ({
  questions,
  isLoading,
  pagination,
  onPageChange,
  onItemsPerPageChange,
}: QuestionsGridViewProps) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="bg-muted mb-2 h-4 w-3/4 rounded"></div>
              <div className="bg-muted mb-4 h-3 w-1/2 rounded"></div>
              <div className="flex gap-2">
                <div className="bg-muted h-6 w-16 rounded"></div>
                <div className="bg-muted h-6 w-20 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <FileTextIcon className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-semibold">No questions found</h3>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {questions.map((question) => (
          <QuestionDisplay
            key={question.id}
            question={question}
            //   onEdit={() => handleEdit(question)}
            //   onView={() => handleView(question)}
            //   onDuplicate={() => handleDuplicate(question)}
            //   onDelete={() => handleDelete(question)}
            // compact
          />
        ))}
      </div>
      {pagination && (
        <DataTablePagination
          {...pagination}
          onPageChange={onPageChange}
          onItemsPerPageChange={onItemsPerPageChange}
        />
      )}
    </div>
  );
};

export default QuestionsGridView;
