import { useMutation } from "@workspace/query";

import { apiClient } from "@/lib/api-client";
import { invalidateQueries } from "@/lib/queries";

import { QuestionCreateInput } from "../schemas/question.schema";

export function useCreateQuestion() {
  return useMutation({
    mutationFn: (data: QuestionCreateInput) => apiClient.TeacherQuestion.createQuestion(data),
    onSuccess: () => {
      // Invalidate and refetch questions list
      invalidateQueries.teachers.questions.all();
    },
    onError: (error: any) => {
      console.error("Create question error:", error);
    },
  });
}
