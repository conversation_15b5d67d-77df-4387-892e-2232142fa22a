import { useQuery } from "@workspace/query";
import { PaginationSearchRequest } from "@workspace/types/shared";

import { apiClient } from "@/lib/api-client";
import { queryKeys } from "@/lib/queries";

export function useGetQuestions(params: PaginationSearchRequest) {
  return useQuery({
    queryKey: queryKeys.teachers.questions.list(params),
    queryFn: () => apiClient.TeacherQuestion.getQuestions(params),
  });
}
