import { QuestionType, QuestionVisibility } from "@workspace/types";
import { z } from "zod";

// Base question schema
export const questionBaseSchema = z.object({
  content: z
    .string()
    .min(1, "Question content is required")
    .max(2000, "Question content is too long"),
  type: z.nativeEnum(QuestionType),
  visibility: z.nativeEnum(QuestionVisibility).optional().default(QuestionVisibility.PRIVATE),
  explanation: z.string().max(1000, "Explanation is too long").optional(),
  difficulty: z
    .number()
    .min(1, "Difficulty must be at least 1")
    .max(5, "Difficulty must be at most 5"),
  tags: z.array(z.string()).optional(),
  subjectId: z.string().optional(),
  topicIds: z.array(z.string()).optional(),
});

// Multiple choice options schema
export const multipleChoiceOptionsSchema = z.object({
  choices: z
    .array(z.string().min(1, "Choice cannot be empty"))
    .min(2, "At least 2 choices required")
    .max(6, "Maximum 6 choices allowed"),
  multipleAnswers: z.boolean().optional().default(false),
});

// True/False group options schema
export const trueFalseGroupOptionsSchema = z.object({
  statements: z
    .array(z.string().min(1, "Statement cannot be empty"))
    .min(1, "At least 1 statement required")
    .max(10, "Maximum 10 statements allowed"),
});

// Number input options schema
export const numberInputOptionsSchema = z.object({
  min: z.number().optional(),
  max: z.number().optional(),
  precision: z.number().min(0).max(10).optional(),
});

// Text input options schema
export const textInputOptionsSchema = z.object({
  maxLength: z.number().min(1).max(5000).optional(),
  multiline: z.boolean().default(false),
});

// Union of all options schemas
export const questionOptionsSchema = z.discriminatedUnion("type", [
  multipleChoiceOptionsSchema,
  trueFalseGroupOptionsSchema,
  numberInputOptionsSchema,
  textInputOptionsSchema,
]);

// Multiple choice correct answer schema
export const multipleChoiceCorrectAnswerSchema = z.object({
  value: z.union([
    z.number().min(0, "Answer index must be non-negative"),
    z
      .array(z.number().min(0, "Answer index must be non-negative"))
      .min(1, "At least one answer required"),
  ]),
});

// True/False group correct answer schema
export const trueFalseGroupCorrectAnswerSchema = z.object({
  value: z.array(z.boolean()).min(1, "At least one answer required"),
});

// Number input correct answer schema
export const numberInputCorrectAnswerSchema = z.object({
  value: z.number(),
});

// Text input correct answer schema
export const textInputCorrectAnswerSchema = z.object({
  value: z.string().min(1, "Answer cannot be empty"),
});

// Union of all correct answer schemas
export const correctAnswerSchema = z.object({
  value: z.union([z.number(), z.array(z.number()), z.array(z.boolean()), z.string()]),
});

// Complete question creation schema
export const questionCreateSchema = questionBaseSchema
  .extend({
    options: questionOptionsSchema,
    correctAnswer: correctAnswerSchema,
  })
  .refine(
    (data) => {
      // Additional validation for multiple choice
      if (data.type === QuestionType.MULTIPLE_CHOICE) {
        const choices = (data.options as z.infer<typeof multipleChoiceOptionsSchema>).choices;
        const multipleAnswers = (data.options as z.infer<typeof multipleChoiceOptionsSchema>)
          .multipleAnswers;
        const answer = (data.correctAnswer as z.infer<typeof multipleChoiceCorrectAnswerSchema>)
          .value;

        if (Array.isArray(answer)) {
          // Multiple answers
          if (!multipleAnswers) {
            return false; // Multiple answers not allowed
          }
          return answer.every((index) => index < choices.length);
        } else {
          // Single answer
          if (multipleAnswers) {
            return false; // Single answer not allowed when multiple answers is enabled
          }
          return answer < choices.length;
        }
      }
      return true;
    },
    {
      message: "Invalid answer selection for multiple choice question",
      path: ["correctAnswer"],
    },
  )
  .refine(
    (data) => {
      // Additional validation for true/false group
      if (data.type === QuestionType.TRUE_FALSE_GROUP) {
        const statements = (data.options as z.infer<typeof trueFalseGroupOptionsSchema>).statements;
        const answers = (data.correctAnswer as z.infer<typeof trueFalseGroupCorrectAnswerSchema>)
          .value;
        return statements.length === answers.length;
      }
      return true;
    },
    {
      message: "Number of answers must match number of statements",
      path: ["correctAnswer"],
    },
  );

// Question update schema (allows partial updates)
export const questionUpdateSchema = questionCreateSchema.partial();

// Question filter schema for search/filtering
export const questionFilterSchema = z.object({
  search: z.string().optional(),
});

// Type exports
export type QuestionCreateInput = z.infer<typeof questionCreateSchema>;
export type QuestionUpdateInput = z.infer<typeof questionUpdateSchema>;
export type QuestionFilterInput = z.infer<typeof questionFilterSchema>;
