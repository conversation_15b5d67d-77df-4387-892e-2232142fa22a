import { QuestionType } from "@workspace/types/domains";

export const getDifficultyColor = (difficulty: number) => {
  switch (difficulty) {
    case 1:
      return "bg-green-100 text-green-800 border-green-200";
    case 2:
      return "bg-blue-100 text-blue-800 border-blue-200";
    case 3:
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    case 4:
      return "bg-orange-100 text-orange-800 border-orange-200";
    case 5:
      return "bg-red-100 text-red-800 border-red-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

export const getDifficultyLabel = (difficulty: number) => {
  switch (difficulty) {
    case 1:
      return "Very Easy";
    case 2:
      return "Easy";
    case 3:
      return "Medium";
    case 4:
      return "Hard";
    case 5:
      return "Very Hard";
    default:
      return "Unknown";
  }
};

export const getQuestionTypeLabel = (type: QuestionType) => {
  switch (type) {
    case QuestionType.MULTIPLE_CHOICE:
      return "Multiple Choice";
    case QuestionType.TRUE_FALSE_GROUP:
      return "True/False";
    case QuestionType.NUMBER_INPUT:
      return "Number";
    case QuestionType.TEXT_INPUT:
      return "Text";
    default:
      return "Unknown";
  }
};
