{"common": {"create": "Create", "creating": "Creating...", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "exit": "Exit"}, "language": {"label": "Language", "english": "English", "vietnamese": "Vietnamese"}, "notFound": {"title": "404 - Page Not Found", "subtitle": "The page you're looking for doesn't exist or has been moved", "description": "Don't worry! This happens sometimes. The page might have been removed, renamed, or you may have followed an incorrect link.", "suggestions": {"title": "What you can try:", "checkUrl": "Double-check the URL for any typos", "goBack": "Go back to the previous page", "visitHome": "Visit our homepage", "useSearch": "Use the search function to find what you're looking for", "contactSupport": "Contact support if you believe this is an error"}, "returnHome": "Go to Homepage", "helpText": "Need help?", "contactSupport": "Contact our support team", "funElement": "Lost in the digital wilderness? 🗺️"}, "navigation": {"home": "Home", "dashboardTeacher": "Teacher Dashboard"}, "auth": {"login": {"title": "Sign In", "subtitle": "Sign in to your account", "signUpButton": "Sign Up", "forgotPassword": "Forgot Password?", "or": "Or", "form": {"emailPlaceholder": "Email or Username", "passwordPlaceholder": "Password", "submitButton": "Sign in", "submittingButton": "Signing in..."}, "success": "Signed in successfully.", "error": {"default": "Sign in failed. Please check your credentials."}}, "register": {"title": "Sign Up", "subtitle": "Create your account", "signInButton": "Sign In", "or": "Or", "form": {"fullNamePlaceholder": "Full Name", "emailPlaceholder": "Email Address", "passwordPlaceholder": "Password", "confirmPasswordPlaceholder": "Confirm Password", "submitButton": "Create Account", "submittingButton": "Creating account..."}, "success": "Account created successfully.", "error": "Account creation failed. Please try again."}, "forgotPassword": {"title": "Forgot Password", "subtitle": "Enter your email to reset your password", "signInButton": "Sign In", "form": {"emailPlaceholder": "Email Address", "submitButton": "Send Reset Link", "submittingButton": "Sending reset link...", "successTitle": "Check your email", "successMessage": "We've sent a password reset link to your email address."}}, "verifyEmail": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Check your email for a verification link", "checkInbox": "Check your inbox for a verification email.", "spamNote": "If you don't see the email, check your spam folder.", "resendButton": "Resend Verification Email", "verifying": "Verifying...", "success": "Email verified successfully. You can now sign in.", "error": "Email verification failed. Please try again.", "signInButton": "Sign In"}, "social": {"google": "Google", "facebook": "Facebook", "apple": "Apple"}, "logout": {"title": "Logout", "success": "You have been logged out."}, "validation": {"emailRequired": "Email or username is required", "emailInvalid": "Please enter a valid email address or username (minimum 3 characters)", "emailRequiredSimple": "Email is required", "emailInvalidSimple": "Please enter a valid email address", "passwordRequired": "Password is required", "passwordMinLength": "Password must be at least {min} characters", "passwordMaxLength": "Password must be less than {max} characters", "passwordComplexity": "Password must contain at least one uppercase letter, one lowercase letter, and one number", "passwordsNoMatch": "Passwords don't match", "fullNameRequired": "First name is required", "fullNameMaxLength": "First name must be less than {max} characters", "resetTokenRequired": "Reset token is required"}}, "home": {"title": "EdTech Frontend", "subtitle": "A modern educational platform with authentication components", "authComponents": {"title": "Authentication Components", "loginPage": "<PERSON><PERSON>", "loginExamples": "Login Examples"}, "buttons": {"title": "Button Variants", "default": "<PERSON><PERSON><PERSON>", "primary": "Primary", "primaryOutline": "Primary Outline", "secondary": "Secondary", "secondaryOutline": "Secondary Outline", "destructive": "Danger", "destructiveOutline": "Danger Outline", "super": "Super", "superOutline": "Super Outline", "ghost": "Ghost", "sidebar": "Sidebar", "sidebarOutline": "Sidebar Outline"}}, "settings": {"title": "Settings"}, "footer": {"company": {"description": "Empowering education through innovative technology. Connect teachers, students, and classrooms in one seamless platform."}, "links": {"platform": {"teachers": "Teachers", "students": "Students", "classrooms": "Classrooms", "questionBanks": "Question Banks"}, "support": {"helpCenter": "Help Center", "contactUs": "Contact Us", "documentation": "Documentation", "status": "Status"}, "legal": {"privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>", "paymentPolicy": "Payment Policy"}}, "sections": {"platform": "Platform", "support": "Support", "contact": "Contact"}, "contact": {"phone": {"label": "Phone", "number": "+****************"}, "address": {"label": "Address", "location": "123 Education Street, Tech City, TC 12345"}}, "newsletter": {"title": "Stay Updated", "description": "Subscribe to our newsletter for the latest updates and educational insights.", "emailPlaceholder": "Enter your email address", "subscribeButton": "Subscribe"}}, "teachers": {"classrooms": {"title": "Classrooms management", "create": {"title": "Create Classroom", "description": "Create a new classroom", "success": "Classroom created successfully."}, "fields": {"name": "Classroom Name", "description": "Description", "type": "Type", "bannerImage": "Banner Image URL", "showInWebsite": "Show in Website"}, "placeholders": {"name": "Enter classroom name", "description": "Enter classroom description", "type": "Select classroom type", "bannerImage": "Enter banner image URL"}, "type": {"public": "Public", "private": "Private"}, "validation": {"nameRequired": "Classroom name is required", "descriptionRequired": "Classroom description is required", "typeRequired": "Classroom type is required", "bannerImageRequired": "Banner image URL is required"}}}}