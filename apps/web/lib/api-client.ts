import { createApiClient } from "@workspace/api-client/index";

const setToken = (name: string, token: string) => {
  if (typeof window !== "undefined") {
    localStorage.setItem(name, token);
  }
};

const getToken = (name: string) => {
  if (typeof window !== "undefined") {
    return localStorage.getItem(name) || "";
  }
  return "";
};

const removeToken = (name: string) => {
  if (typeof window !== "undefined") {
    localStorage.removeItem(name);
  }
};

export const apiClient = createApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL!,
  setToken,
  getToken,
  removeToken,
});
