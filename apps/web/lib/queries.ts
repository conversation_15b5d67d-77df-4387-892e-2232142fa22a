import { queryClient } from "@workspace/query/client";
import { PaginationSearchRequest } from "@workspace/types/shared";

// Query keys factory for better organization
export const queryKeys = {
  // Auth related queries
  auth: {
    user: () => ["auth", "user"] as const,
  },
  teachers: {
    all: () => ["teachers"] as const,
    classrooms: {
      all: () => ["teachers", "classrooms"] as const,
      list: (request: PaginationSearchRequest) => ["teachers", "classrooms", request] as const,
      detail: (idOrCode: string) => ["teachers", "classrooms", idOrCode] as const,
    },
    questions: {
      all: () => ["teachers", "questions"] as const,
      list: (request: PaginationSearchRequest) => ["teachers", "questions", request] as const,
      detail: (id: string) => ["teachers", "questions", id] as const,
    },
  },
} as const;

// Helper function to invalidate related queries
export const invalidateQueries = {
  auth: () => queryClient.invalidateQueries({ queryKey: queryKeys.auth.user() }),
  teachers: {
    all: () => queryClient.invalidateQueries({ queryKey: queryKeys.teachers.all() }),
    classrooms: {
      all: () => queryClient.invalidateQueries({ queryKey: queryKeys.teachers.classrooms.all() }),
      list: (request: PaginationSearchRequest) =>
        queryClient.invalidateQueries({
          queryKey: queryKeys.teachers.classrooms.list(request),
        }),
      detail: (idOrCode: string) =>
        queryClient.invalidateQueries({
          queryKey: queryKeys.teachers.classrooms.detail(idOrCode),
        }),
    },
    questions: {
      all: () => queryClient.invalidateQueries({ queryKey: queryKeys.teachers.questions.all() }),
      list: (request: PaginationSearchRequest) =>
        queryClient.invalidateQueries({
          queryKey: queryKeys.teachers.questions.list(request),
        }),
      detail: (id: string) =>
        queryClient.invalidateQueries({
          queryKey: queryKeys.teachers.questions.detail(id),
        }),
    },
  },
};
