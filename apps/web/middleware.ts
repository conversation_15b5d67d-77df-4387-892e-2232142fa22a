import { NextRequest } from "next/server";
import createMiddleware from "next-intl/middleware";

import { routing } from "./i18n/routing";

export default async function middleware(request: NextRequest) {
  // Handle internationalization
  const handleI18nRouting = createMiddleware(routing);
  return handleI18nRouting(request);
}

export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next|api|p).*)"],
};
