{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --no-lint", "start": "next start", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "docker:build": "docker build -t edtech-web .", "docker:run": "docker run -p 3000:3000 edtech-web", "docker:up": "docker-compose up --build", "docker:down": "docker-compose down"}, "dependencies": {"@tanstack/react-table": "^8.21.3", "@workspace/ui": "workspace:*", "date-fns": "^4.1.0", "lucide-react": "^0.475.0", "next": "^15.4.5", "next-intl": "^4.3.9", "next-themes": "^0.4.6", "nextjs-toploader": "^3.9.17", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hot-toast": "^2.6.0", "zustand": "^5.0.8"}, "devDependencies": {"@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.9.2"}}