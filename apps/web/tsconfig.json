{"extends": "@workspace/typescript-config/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "@workspace/ui/*": ["../../packages/ui/src/*"], "@workspace/query/*": ["../../packages/query/src/*"], "@workspace/api-client/*": ["../../packages/api-client/src/*"], "@workspace/types/*": ["../../packages/types/src/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "next.config.ts", "middleware.ts", "i18n/*.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "../../packages/api-client/src/core/services/teachers/classrooms/classroom.service.ts"], "exclude": ["node_modules"]}