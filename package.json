{"name": "edtech-fe-monorepo", "version": "0.0.1", "private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@workspace/api-client": "workspace:*", "@workspace/eslint-config": "workspace:*", "@workspace/query": "workspace:*", "@workspace/types": "workspace:*", "@workspace/typescript-config": "workspace:*", "@workspace/ui": "workspace:*", "husky": "^9.1.7", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "turbo": "^2.5.5", "typescript": "5.7.3"}, "packageManager": "bun@1.2.0", "workspaces": ["apps/*", "packages/*"], "lint-staged": {"apps/web/**/*.{js,ts,tsx}": "eslint --config apps/web/eslint.config.js --fix", "packages/ui/**/*.{js,ts}": "eslint --config packages/ui/eslint.config.js --fix"}, "engines": {"node": ">=20"}, "dependencies": {"@react-oauth/google": "^0.12.2", "dayjs": "^1.11.18"}}