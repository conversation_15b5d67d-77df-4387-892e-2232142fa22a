import { Service } from "./abstracts/service.abstract";
import { createAxiosInstance } from "./axios-instance";
import { ApiClientConfig } from "./interfaces";
import { AuthService } from "./services/auth/auth.service";
import { ContentClassroomService } from "./services/contents/classroom.service";
import { ContentExamService } from "./services/contents/exam.service";
import { ContentQuestionService } from "./services/contents/question.service";
import { TeacherClassroomService } from "./services/teachers/classrooms/classroom.service";
import { TeacherQuestionService } from "./services/teachers/questions/question.service";
import { UserService } from "./services/user/user.service";

const createApiClient = (config: ApiClientConfig) => {
  const axiosInstance = createAxiosInstance(config);
  Service.apiClient = axiosInstance;

  return {
    axiosInstance,

    ContentExam: ContentExamService,
    ContentQuestion: ContentQuestionService,
    ContentClassroom: ContentClassroomService,

    Auth: AuthService,
    User: UserService,
    TeacherClassroom: TeacherClassroomService,
    TeacherQuestion: TeacherQuestionService,
  };
};

export default createApiClient;
