import axios, { AxiosError, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import { ApiClientConfig } from "./interfaces";

// Extended axios request config type
interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

export const createAxiosInstance = (config: ApiClientConfig) => {
  const { baseURL, setToken, getToken, removeToken } = config;

  // Create axios instance with default config
  const axiosInstance = axios.create({
    baseURL,
    timeout: 15000,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // Check if we're on the client side
  const isClient = typeof window !== "undefined";

  // Store for queued requests
  let isRefreshing = false;
  let failedQueue: {
    resolve: (value?: unknown) => void;
    reject: (error?: unknown) => void;
    config: ExtendedAxiosRequestConfig;
  }[] = [];

  // Logout function to centralize logout logic
  const performLogout = () => {
    removeToken("accessToken");
    removeToken("refreshToken");
    // Clear any other user-related data here

    // Redirect to login page
    if (isClient) {
      window.location.href = "/auth/login";
    }
  };

  const processQueue = (error: AxiosError | null, token: string | null = null) => {
    failedQueue.forEach((prom) => {
      if (error) {
        prom.reject(error);
      } else {
        if (token) {
          prom.config.headers.Authorization = `Bearer ${token}`;
        }
        prom.resolve(axiosInstance(prom.config));
      }
    });
    failedQueue = [];
  };

  // Request interceptor to add auth token and language header
  axiosInstance.interceptors.request.use(
    (config) => {
      const token = getToken("accessToken");
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      const contentApiKey = isClient ? null : process.env.CONTENT_API_KEY;
      if (contentApiKey) {
        config.headers["x-api-key"] = contentApiKey;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    },
  );

  const excludedPaths = [
    "/auth/sign-in",
    "/auth/sign-up",
    "/auth/verify-email",
    "/auth/resend-verification",
    "/auth/request-password-reset",
    "/auth/reset-password",
    "/auth/google",
    "/auth/refresh",
  ];

  // Response interceptor for error handling
  axiosInstance.interceptors.response.use(
    (response: AxiosResponse) => {
      return response;
    },
    async (error: AxiosError) => {
      const originalRequest = error.config as ExtendedAxiosRequestConfig;
      // If error is 401 and we're not already refreshing
      if (
        error.response?.status === 401 &&
        !originalRequest._retry &&
        !excludedPaths.includes(originalRequest.url || "")
      ) {
        if (isRefreshing) {
          // If refreshing, queue the request
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject, config: originalRequest });
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          const refreshToken = getToken("refreshToken");
          if (!refreshToken) {
            throw new Error("No refresh token available");
          }

          // Call refresh token endpoint
          const response = await axiosInstance.post("/auth/refresh", {
            refreshToken,
          });

          const { accessToken, refreshToken: newRefreshToken } = response.data;

          // Validate that we received a valid access token
          if (!accessToken) {
            throw new Error("Invalid access token received");
          }

          // Store new tokens
          setToken("accessToken", accessToken);
          if (newRefreshToken) {
            setToken("refreshToken", newRefreshToken);
          }

          // Update authorization header
          axiosInstance.defaults.headers.common["Authorization"] = `Bearer ${accessToken}`;
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;

          // Process queued requests with success
          processQueue(null, accessToken);

          // Retry original request
          return axiosInstance(originalRequest);
        } catch (refreshError) {
          console.error("Token refresh failed:", refreshError);

          // Process queued requests with error
          processQueue(error, null);

          // Perform logout
          performLogout();

          // Create a more descriptive error for the failed request
          const logoutError = new AxiosError(
            "Session expired. Please log in again.",
            "AUTHENTICATION_FAILED",
            originalRequest,
            null,
            {
              ...error.response,
              status: 401,
              statusText: "Unauthorized - Session Expired",
            } as any,
          );

          return Promise.reject(logoutError);
        } finally {
          isRefreshing = false;
        }
      }

      // Handle refresh token endpoint 401 specifically
      if (error.response?.status === 401 && originalRequest.url === "/auth/refresh") {
        console.error("Refresh token is invalid or expired");
        performLogout();
        return Promise.reject(error);
      }

      // Handle other errors
      if (error.response?.status === 403) {
        console.error("Access forbidden");
      } else if (error.response?.status && error.response.status >= 500) {
        console.error("Server error:", error.response?.data);
      }

      return Promise.reject(error);
    },
  );

  return axiosInstance;
};
