import { ExamStatus, ExamType } from "@workspace/types";

export interface ExamCreateData {
  title: string;
  description: string;
  showInWebsite: boolean;
  instructions: string;
  status: ExamStatus;
  type: ExamType;
  totalQuestions: number;
  totalPoints: number;
  settings: {
    allowRetake: boolean;
    showCorrectAnswers: boolean;
    shuffleQuestions: boolean;
    shuffleOptions: boolean;
    maxAttempts: number;
    timeLimit: number;
  };
  classroomId?: string;
  subjectId: string;
}
