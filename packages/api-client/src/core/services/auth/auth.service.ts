import { AuthResponse } from "@workspace/types";
import { Service } from "../../abstracts/service.abstract";
import { LoginData, RegisterData } from "../../interfaces/auth.interface";

export class AuthService extends Service {
  static async login(data: LoginData) {
    const response = await Service.apiClient.post<AuthResponse>("/auth/sign-in", data);
    return response.data;
  }

  static async register(data: RegisterData) {
    const response = await Service.apiClient.post<AuthResponse>("/auth/sign-up", data);
    return response.data;
  }

  static async verifyEmail(token: string) {
    const response = await Service.apiClient.post<AuthResponse>("/auth/verify-email", {
      token,
    });
    return response.data;
  }

  static async googleLogin(code: string) {
    const response = await Service.apiClient.post<AuthResponse>("/auth/google", {
      code,
    });
    return response.data;
  }

  static async googleOneTapLogin(credential: string) {
    const response = await Service.apiClient.post<AuthResponse>("/auth/google-token", {
      idToken: credential,
    });
    return response.data;
  }
}
