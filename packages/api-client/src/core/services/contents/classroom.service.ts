import { Service } from "../../abstracts/service.abstract";
import { Classroom, PaginationResponse, PaginationSearchRequest } from "@workspace/types";

export class ContentClassroomService extends Service {
  static async getClassrooms(request: PaginationSearchRequest) {
    const response = await Service.apiClient.get<PaginationResponse<Classroom[]>>(
      "/contents/classrooms",
      { params: request },
    );
    return response.data;
  }
}
