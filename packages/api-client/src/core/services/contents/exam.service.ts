import { Exam, PaginationResponse, PaginationSearchRequest } from "@workspace/types";
import { Service } from "../../abstracts/service.abstract";

export class ContentExamService extends Service {
  static async getExams(request: PaginationSearchRequest) {
    const response = await Service.apiClient.get<PaginationResponse<Exam[]>>("/contents/exams", {
      params: request,
    });
    return response.data;
  }
}
