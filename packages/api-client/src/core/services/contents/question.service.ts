import { PaginationResponse, PaginationSearchRequest, Question } from "@workspace/types";
import { Service } from "../../abstracts/service.abstract";

export class ContentQuestionService extends Service {
  static async getQuestions(request: PaginationSearchRequest) {
    const response = await Service.apiClient.get<PaginationResponse<Question[]>>(
      "/contents/questions",
      {
        params: request,
      },
    );
    return response.data;
  }

  static async getQuestion(id: string) {
    const response = await Service.apiClient.get<Question>(`/contents/questions/${id}`);
    return response.data;
  }
}
