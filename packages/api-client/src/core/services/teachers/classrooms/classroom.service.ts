// Import the type for classroom creation
import { Classroom, PaginationResponse, PaginationSearchRequest } from "@workspace/types";
import { Service } from "../../../abstracts/service.abstract";
import { ClassroomCreateData } from "../../../interfaces/classroom.interface";

export class TeacherClassroomService extends Service {
  static async getClassrooms(request: PaginationSearchRequest) {
    const response = await Service.apiClient.get<PaginationResponse<Classroom[]>>(
      "/teachers/classrooms",
      {
        params: request,
      },
    );
    return response.data;
  }

  static async getClassroom(idOrCode: string) {
    const response = await Service.apiClient.get<Classroom>(`/teachers/classrooms/${idOrCode}`);
    return response.data;
  }

  static async createClassroom(data: ClassroomCreateData) {
    const response = await Service.apiClient.post<Classroom>("/teachers/classrooms", data);
    return response.data;
  }
}
