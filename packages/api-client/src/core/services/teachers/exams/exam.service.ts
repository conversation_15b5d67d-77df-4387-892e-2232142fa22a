import { Exam, PaginationResponse, PaginationSearchRequest } from "@workspace/types";
import { Service } from "../../../abstracts/service.abstract";
import { ExamCreateData } from "../../../interfaces/exam.interface";

export class TeacherExamService extends Service {
  static async getExams(request: PaginationSearchRequest) {
    const response = await Service.apiClient.get<PaginationResponse<Exam[]>>("/teachers/exams", {
      params: request,
    });
    return response.data;
  }

  static async getExam(id: string) {
    const response = await Service.apiClient.get<Exam>(`/teachers/exams/${id}`);
    return response.data;
  }

  static async createExam(data: ExamCreateData) {
    const response = await Service.apiClient.post<Exam>("/teachers/exams", data);
    return response.data;
  }

  static async updateExam(id: string, data: Partial<ExamCreateData>) {
    const response = await Service.apiClient.patch<Exam>(`/teachers/exams/${id}`, data);
    return response.data;
  }

  static async publishExam(id: string) {
    const response = await Service.apiClient.post<Exam>(`/teachers/exams/${id}/publish`);
    return response.data;
  }
}
