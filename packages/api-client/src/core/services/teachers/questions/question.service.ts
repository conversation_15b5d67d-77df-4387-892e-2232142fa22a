// Import the type for Question creation
import { Question, PaginationResponse, PaginationSearchRequest } from "@workspace/types";
import { Service } from "../../../abstracts/service.abstract";
import { QuestionCreateData } from "../../../interfaces/question.interface";

export class TeacherQuestionService extends Service {
  static async getQuestions(request: PaginationSearchRequest) {
    const response = await Service.apiClient.get<PaginationResponse<Question[]>>(
      "/teachers/questions",
      {
        params: request,
      },
    );
    return response.data;
  }

  static async getQuestion(id: string) {
    const response = await Service.apiClient.get<Question>(`/teachers/questions/${id}`);
    return response.data;
  }

  static async createQuestion(data: QuestionCreateData) {
    const response = await Service.apiClient.post<Question>("/teachers/questions", data);
    return response.data;
  }

  static async updateQuestion(id: string, data: Partial<QuestionCreateData>) {
    const response = await Service.apiClient.put<Question>(`/teachers/questions/${id}`, data);
    return response.data;
  }

  static async deleteQuestion(id: string) {
    const response = await Service.apiClient.delete(`/teachers/questions/${id}`);
    return response.data;
  }
}
