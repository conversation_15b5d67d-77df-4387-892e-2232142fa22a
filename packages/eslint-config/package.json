{"name": "@workspace/eslint-config", "version": "0.0.0", "type": "module", "private": true, "exports": {"./base": "./base.js", "./next-js": "./next.js", "./react-internal": "./react-internal.js"}, "devDependencies": {"@next/eslint-plugin-next": "^15.4.5", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.32.0", "eslint-config-prettier": "^9.1.2", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prettier": "^5.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-turbo": "^2.5.5", "eslint-plugin-unused-imports": "^4.2.0", "globals": "^15.15.0", "typescript": "^5.9.2", "typescript-eslint": "^8.39.0"}}