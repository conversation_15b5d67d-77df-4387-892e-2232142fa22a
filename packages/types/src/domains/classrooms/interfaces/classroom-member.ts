import { BaseEntity } from "../../../shared/interfaces/entity";
import { User } from "../../users/interfaces/user";
import { ClassroomMemberStatus } from "../enums/classroom-member-status";
import { MemberRole } from "../enums/member-role";
import { Classroom } from "./classroom";

export interface ClassroomMember extends BaseEntity {
  status: ClassroomMemberStatus;
  joinedAt: Date;
  approvedAt?: Date;
  role: MemberRole;
  classroomId: string;
  classroom: Classroom;
  userId: string;
  user: User;
}
