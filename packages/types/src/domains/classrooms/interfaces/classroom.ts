import { BaseEntity } from "../../../shared/interfaces/entity";
import { User } from "../../users/interfaces/user";
import { ClassroomStatus } from "../enums/classroom-status";
import { ClassroomType } from "../enums/classroom-type";
import { ClassroomMember } from "./classroom-member";

export interface Classroom extends BaseEntity {
  name: string;
  code: string;
  description?: string;
  type: ClassroomType;
  showInWebsite: boolean;
  bannerImage?: string;
  status: ClassroomStatus;
  userId: string;
  user: User;
  classroomMembers: ClassroomMember[];
  totalMembers: number;
  totalExams: number;
}
