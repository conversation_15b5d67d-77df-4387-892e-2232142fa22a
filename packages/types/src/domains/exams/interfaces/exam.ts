import { BaseEntity } from "@workspace/types/shared";
import { Classroom } from "../../classrooms";
import { ExamStatus } from "../enums/exam-status";
import { ExamType } from "../enums";

export interface Exam extends BaseEntity {
  title: string;
  description: string;
  showInWebsite: boolean;
  instructions: string;
  status: ExamStatus;
  type: ExamType;
  publishedAt?: string;
  totalQuestions: number;
  totalPoints: number;
  settings: {
    allowRetake: boolean;
    showCorrectAnswers: boolean;
    shuffleQuestions: boolean;
    shuffleOptions: boolean;
    maxAttempts: number;
    timeLimit: number;
  };
  classroomId?: string;
  classroom?: Classroom;
  subjectId: string;
  userId: string;
  totalAttempts: number;
  totalEnrolledUsers: number;
}
