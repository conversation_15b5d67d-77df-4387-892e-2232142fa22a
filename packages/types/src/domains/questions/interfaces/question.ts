import { BaseEntity } from "@workspace/types/shared";
import { QuestionType } from "../enums/question-type";
import { QuestionOptions } from "../types/question-options";
import { CorrectAnswer } from "../types/correct-answer";
import { User } from "../../users";
import { Topic } from "../../topics";
import { Subject } from "../../subjects";

export interface Question extends BaseEntity {
  content: string;
  type: QuestionType;
  options: QuestionOptions;
  correctAnswer: CorrectAnswer;
  explanation?: string;
  difficulty: number;
  tags?: string[];
  subjectId?: string;
  userId: string;
  topics: Topic[];
  subject?: Subject;
  user: User;
}
