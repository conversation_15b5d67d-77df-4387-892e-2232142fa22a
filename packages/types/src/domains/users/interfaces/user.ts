import { BaseEntity } from "../../../shared/interfaces/entity";
import { UserGender } from "../enums/user-gender";
import { UserStatus } from "../enums/user-status";

export interface User extends BaseEntity {
  fullName: string;
  email: string;
  avatar?: string;
  gender?: UserGender;
  phoneNumber?: string;
  dateOfBirth?: string;
  isVerified: boolean;
  isTeacher: boolean;
  isModerator: boolean;
  isAdmin: boolean;
  status: UserStatus;
  suspendedAt?: string;
  suspendedReason?: string;
  preferences?: any;
  lastLoginAt?: Date;
  teacher?: any;
}
