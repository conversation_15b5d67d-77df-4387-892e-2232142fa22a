import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { ScrollArea } from "@workspace/ui/components/scroll-area";
import { cn } from "@workspace/ui/lib/utils";

interface BaseDialogProps {
  children: React.ReactNode;
  className?: string;
  trigger?: React.ReactNode;
  title?: React.ReactNode;
  description?: React.ReactNode;
  open?: boolean;
  onClose?: () => void;
  footer?: React.ReactNode;
}

const BaseDialog = ({
  children,
  className,
  trigger,
  title,
  description,
  open,
  onClose,
  footer,
}: BaseDialogProps) => {
  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) {
          onClose?.();
        }
      }}
    >
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent
        className={cn("flex max-h-[min(600px,80vh)] flex-col gap-0 p-0 xl:max-w-xl", className)}
      >
        <DialogHeader className="space-y-0 border-b-2 px-6 py-4 text-left">
          {title && <DialogTitle className="">{title}</DialogTitle>}
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        <ScrollArea className="flex max-h-full flex-col overflow-hidden">
          <div className="p-4">{children}</div>
        </ScrollArea>
        <DialogFooter>{footer}</DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BaseDialog;
