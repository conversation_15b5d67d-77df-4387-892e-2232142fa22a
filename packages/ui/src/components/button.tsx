import { Slot } from "@radix-ui/react-slot";
import { cn } from "@workspace/ui/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

const buttonVariants = cva(
  "cursor-pointer inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-bold transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive uppercase tracking-wide",
  {
    variants: {
      variant: {
        default:
          "bg-white text-black border-slate-200 border-2 border-b-4 active:border-b-2 hover:bg-slate-100 text-slate-500",
        primary:
          "bg-primary-400 text-primary-foreground hover:bg-primary-400/90 border-primary-500 border-b-4 active:border-b-0",
        primaryOutline:
          "bg-primary-500/15 text-primary-500 border-primary-300 border-2 hover:bg-primary-500/20",
        secondary:
          "bg-secondary-500 text-primary-foreground hover:bg-secondary-500/90 border-secondary-600 border-b-4 active:border-b-0",
        secondaryOutline:
          "bg-secondary-500/15 text-secondary-500 border-secondary-300 border-2 hover:bg-secondary-500/20",
        destructive:
          "bg-destructive-500 text-primary-foreground hover:bg-destructive-500/90 border-destructive-600 border-b-4 active:border-b-0",
        destructiveOutline:
          "bg-destructive-500/15 text-destructive-500 border-destructive-300 border-2 hover:bg-destructive-500/20",
        super:
          "bg-indigo-500 text-primary-foreground hover:bg-indigo-500/90 border-indigo-600 border-b-4 active:border-b-0",
        superOutline:
          "bg-indigo-500/15 text-indigo-500 border-indigo-300 border-2 hover:bg-indigo-500/20",
        ghost: "text-accent-500 hover:bg-accent-100 hover:border-2",
        ghostOutline: "text-accent-500 hover:bg-accent-100 border-2 active:border-b-2 border-b-4",
        sidebar: "text-foreground hover:text-foreground/90 hover:bg-foreground/5",
        sidebarOutline: "bg-sky-500/15 text-sky-500 border-sky-300 border-2 hover:bg-sky-500/20",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-11 px-4 py-2",
        sm: "h-9 px-3",
        lg: "h-12 px-8",
        icon: "size-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
  }) {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}

export { Button, buttonVariants };
