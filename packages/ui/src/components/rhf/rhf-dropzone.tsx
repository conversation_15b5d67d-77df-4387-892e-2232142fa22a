import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { cn } from "@workspace/ui/lib/utils";
import { FileIcon, ImageIcon, Upload, XCircleIcon } from "lucide-react";
import * as React from "react";
import { useState } from "react";
import { useDropzone } from "react-dropzone";
import { Control, FieldPath, FieldValues } from "react-hook-form";

interface RHFDropzoneProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  label?: string;
  description?: string;
  disabled?: boolean;
  className?: string;
  accept?: Record<string, string[]>;
  maxFiles?: number;
  maxSize?: number; // in bytes
  showPreview?: boolean;
  previewClassName?: string;
}

const FilePreview = ({
  file,
  onRemove,
  className,
}: {
  file: File | string;
  onRemove: () => void;
  className?: string;
}) => {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  React.useEffect(() => {
    if (typeof file === "string") {
      setPreviewUrl(file);
    } else if (file instanceof File && file.type.startsWith("image/")) {
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
      return () => URL.revokeObjectURL(url);
    } else {
      setPreviewUrl(null);
    }

    return;
  }, [file]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (previewUrl) {
    // Image preview
    return (
      <div className={cn("relative aspect-square", className)}>
        <button type="button" className="absolute -top-2 -right-2 z-10" onClick={onRemove}>
          <XCircleIcon className="fill-primary text-primary-foreground h-5 w-5" />
        </button>
        <img
          src={previewUrl}
          alt={typeof file === "string" ? "Preview" : file.name}
          className="border-border h-full w-full rounded-md border object-cover"
        />
      </div>
    );
  }

  // File preview (non-image)
  if (!(file instanceof File)) return null;

  return (
    <div className={cn("relative rounded-md border p-4", className)}>
      <button type="button" className="absolute -top-2 -right-2 z-10" onClick={onRemove}>
        <XCircleIcon className="fill-primary text-primary-foreground h-5 w-5" />
      </button>
      <div className="flex items-center gap-3">
        <FileIcon className="text-muted-foreground h-8 w-8" />
        <div className="flex-1">
          <p className="truncate text-sm font-medium">{file.name}</p>
          <p className="text-muted-foreground text-xs">
            {formatFileSize(file.size)} • {file.type || "Unknown type"}
          </p>
        </div>
      </div>
    </div>
  );
};

const DropzoneField = ({
  onChange,
  accept,
  maxFiles,
  maxSize,
  disabled,
}: {
  onChange: (file: File | null | { error: string }) => void;
  accept: Record<string, string[]>;
  maxFiles: number;
  maxSize: number;
  disabled?: boolean;
}) => {
  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } = useDropzone({
    onDrop: (acceptedFiles, rejectedFiles) => {
      const file = acceptedFiles[0];
      if (file) {
        onChange(file);
      } else if (rejectedFiles.length > 0) {
        // Handle file size error
        const fileError = rejectedFiles[0]?.errors[0];
        if (fileError?.code === "file-too-large") {
          onChange({
            error: `File is too large. Max size is ${Math.round(maxSize / (1024 * 1024))}MB.`,
          });
        }
      }
    },
    accept,
    maxFiles,
    maxSize,
    disabled,
  });

  return (
    <div
      {...getRootProps()}
      className={cn(
        "focus:border-primary flex min-h-32 cursor-pointer flex-col items-center justify-center rounded-md border border-dashed p-6 transition-colors focus:outline-none",
        {
          "border-primary bg-secondary": isDragActive && isDragAccept,
          "border-destructive bg-destructive/20": isDragActive && isDragReject,
          "cursor-not-allowed opacity-50": disabled,
        },
      )}
    >
      <input {...getInputProps()} />
      <div className="flex flex-col items-center gap-2 text-center">
        {accept["image/*"] ? (
          <ImageIcon className="text-muted-foreground h-8 w-8" strokeWidth={1.25} />
        ) : (
          <Upload className="text-muted-foreground h-8 w-8" strokeWidth={1.25} />
        )}
        <div className="text-sm">
          {isDragActive ? (
            <p className="text-primary">Drop the file here...</p>
          ) : (
            <div>
              <p className="font-medium">Click to upload or drag and drop</p>
              <p className="text-muted-foreground">
                {Object.values(accept).flat().join(", ")} up to{" "}
                {Math.round(maxSize / (1024 * 1024))}MB
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const RHFDropzone = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  label,
  description,
  disabled,
  className,
  accept = {
    "image/*": [".png", ".jpg", ".jpeg", ".webp"],
    "application/pdf": [".pdf"],
    "text/*": [".txt", ".md"],
    "application/msword": [".doc"],
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [".docx"],
  },
  maxFiles = 1,
  maxSize = 10 * 1024 * 1024, // 10MB default
  showPreview = true,
  previewClassName,
}: RHFDropzoneProps<TFieldValues, TName>) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field: { onChange, value } }) => (
        <FormItem className={className}>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <div className="w-full">
              {value && showPreview ? (
                <FilePreview
                  file={value}
                  onRemove={() => onChange(null)}
                  className={previewClassName}
                />
              ) : (
                <DropzoneField
                  onChange={onChange}
                  accept={accept}
                  maxFiles={maxFiles}
                  maxSize={maxSize}
                  disabled={disabled}
                />
              )}
            </div>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage>{value?.error && value.error}</FormMessage>
        </FormItem>
      )}
    />
  );
};

export default RHFDropzone;
