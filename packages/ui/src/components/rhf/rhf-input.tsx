import { But<PERSON> } from "@workspace/ui/components/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Eye, EyeOff } from "lucide-react";
import * as React from "react";
import { Control, FieldPath, FieldValues } from "react-hook-form";

interface RHFInputProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> extends Omit<React.ComponentProps<typeof Input>, "name"> {
  control: Control<TFieldValues>;
  name: TName;
  label?: string;
  description?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showPasswordToggle?: boolean; // For password inputs
}

const RHFInput = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  label,
  description,
  placeholder,
  disabled,
  className,
  type = "text",
  showPasswordToggle = false,
  ...inputProps
}: RHFInputProps<TFieldValues, TName>) => {
  const [showPassword, setShowPassword] = React.useState(false);

  // Determine if this is a password field
  const isPasswordField = type === "password" || showPasswordToggle;
  const inputType = isPasswordField && showPassword ? "text" : type;

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            {isPasswordField ? (
              <div className="relative">
                <Input
                  className="border-2"
                  type={inputType}
                  placeholder={placeholder}
                  disabled={disabled}
                  {...field}
                  {...inputProps}
                />
                <Button
                  type="button"
                  variant="ghostOutline"
                  size="icon"
                  className="absolute top-0 right-0 h-full border-none px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={disabled}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            ) : (
              <Input
                className="border-2"
                type={type}
                placeholder={placeholder}
                disabled={disabled}
                {...field}
                {...inputProps}
              />
            )}
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default RHFInput;
