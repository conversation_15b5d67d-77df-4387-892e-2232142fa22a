import RHFInput from "@workspace/ui/components/rhf/rhf-input";
import { Control, FieldPath, FieldValues } from "react-hook-form";

interface RHFPasswordInputProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  label?: string;
  description?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

const RHFPasswordInput = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: RHFPasswordInputProps<TFieldValues, TName>,
) => {
  return <RHFInput {...props} type="password" showPasswordToggle={true} />;
};

export default RHFPasswordInput;
