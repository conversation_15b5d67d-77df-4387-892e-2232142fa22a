import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { cn } from "@workspace/ui/lib/utils";
import { CircleCheckIcon } from "lucide-react";
import { Control, FieldPath, FieldValues } from "react-hook-form";

interface RadioOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: React.ReactNode;
}

interface RHFRadioGroupProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> {
  control: Control<TFieldValues>;
  name: TName;
  label?: string;
  description?: string;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  options: RadioOption[];
}

const RHFRadioGroup = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  label,
  description,
  placeholder,
  disabled,
  className,
  options,
}: RHFRadioGroupProps<TFieldValues, TName>) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <RadioGroupPrimitive.Root
              value={field.value} // Bind the current form value
              onValueChange={field.onChange} // Update form value on change
              disabled={disabled}
              className="grid w-full grid-cols-2 gap-4 md:grid-cols-4"
            >
              {options.map((option) => (
                <RadioGroupPrimitive.Item
                  key={option.value}
                  value={option.value}
                  disabled={option.disabled}
                  className={cn(
                    "group ring-border relative rounded px-3 py-2 text-start ring-[1px]",
                    "data-[state=checked]:ring-primary data-[state=checked]:ring-2",
                  )}
                >
                  <CircleCheckIcon
                    className={cn(
                      "text-primary fill-primary absolute top-0 right-0 h-6 w-6 translate-x-1/2 -translate-y-1/2 stroke-white",
                      "group-data-[state=unchecked]:hidden",
                    )}
                  />
                  <div className="text-muted-foreground mb-2.5">{option.icon}</div>
                  <span className="font-semibold">{option.label}</span>
                </RadioGroupPrimitive.Item>
              ))}
            </RadioGroupPrimitive.Root>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default RHFRadioGroup;
