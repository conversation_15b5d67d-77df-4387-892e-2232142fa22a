import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { Slider } from "@workspace/ui/components/slider";
import * as React from "react";
import { Control, FieldPath, FieldValues } from "react-hook-form";

interface RHFSliderProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> extends Omit<React.ComponentProps<typeof Slider>, "name" | "value" | "onValueChange"> {
  control: Control<TFieldValues>;
  name: TName;
  label?: string;
  description?: string;
  disabled?: boolean;
  className?: string;
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
}

const RHFSlider = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  control,
  name,
  label,
  description,
  disabled,
  className,
  min = 0,
  max = 100,
  step = 1,
  unit,
  ...sliderProps
}: RHFSliderProps<TFieldValues, TName>) => {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormControl>
            <>
              <div className="flex items-center justify-between">
                {label && <FormLabel>{label}</FormLabel>}
                <div className="flex items-center gap-2">
                  <Input
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                    className="w-16"
                  />
                  {unit && <span className="text-muted-foreground text-sm">{unit}</span>}
                </div>
              </div>
              <Slider
                min={min}
                max={max}
                step={step}
                disabled={disabled}
                value={[Number(field.value)]}
                onValueChange={([value]) => field.onChange(value)}
                {...sliderProps}
              />
            </>
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default RHFSlider;
