@import "tailwindcss";
@source "../../../apps/**/*.{ts,tsx}";
@source "../../../components/**/*.{ts,tsx}";
@source "../**/*.{ts,tsx}";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: theme(colors.white);
  --foreground: theme(colors.black);
  --card: theme(colors.white);
  --card-foreground: theme(colors.black);
  --popover: theme(colors.white);
  --popover-foreground: theme(colors.black);
  --primary: theme(colors.orange.500);
  --primary-foreground: theme(colors.slate.950);
  --secondary: theme(colors.teal.500);
  --secondary-foreground: theme(colors.slate.950);
  --muted: theme(colors.slate.50);
  --muted-foreground: theme(colors.slate.900);
  --accent: theme(colors.gray.200);
  --accent-foreground: theme(colors.slate.950);
  --destructive: theme(colors.rose.500);
  --destructive-foreground: theme(colors.slate.950);
  --border: theme(colors.slate.200);
  --input: theme(colors.slate.200);
  --ring: theme(colors.orange.500);
  --chart-1: #ff3333;
  --chart-2: #ffff00;
  --chart-3: #0066ff;
  --chart-4: #00cc00;
  --chart-5: #cc00cc;
  --sidebar: theme(colors.slate.50);
  --sidebar-foreground: theme(colors.black);
  --sidebar-primary: theme(colors.orange.500);
  --sidebar-primary-foreground: theme(colors.slate.950);
  --sidebar-accent: theme(colors.gray.200);
  --sidebar-accent-foreground: theme(colors.slate.950);
  --sidebar-border: theme(colors.black);
  --sidebar-ring: theme(colors.orange.500);
  --font-sans: Quicksand, ui-sans-serif, sans-serif, system-ui;
  --radius: 0.625rem;
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  --success: theme(colors.green.600);
}

.dark {
  --background: theme(colors.slate.950);
  --foreground: theme(colors.slate.50);
  --card: theme(colors.slate.900);
  --card-foreground: theme(colors.slate.50);
  --popover: theme(colors.slate.900);
  --popover-foreground: theme(colors.slate.50);
  --primary: theme(colors.orange.500);
  --primary-foreground: theme(colors.slate.950);
  --secondary: theme(colors.teal.500);
  --secondary-foreground: theme(colors.slate.950);
  --muted: theme(colors.slate.800);
  --muted-foreground: theme(colors.slate.400);
  --accent: theme(colors.slate.800);
  --accent-foreground: theme(colors.slate.50);
  --destructive: theme(colors.rose.600);
  --destructive-foreground: theme(colors.slate.950);
  --border: theme(colors.slate.800);
  --input: theme(colors.slate.800);
  --ring: theme(colors.orange.500);
  --chart-1: #ff3333;
  --chart-2: #ffff00;
  --chart-3: #0066ff;
  --chart-4: #00cc00;
  --chart-5: #cc00cc;
  --sidebar: theme(colors.slate.900);
  --sidebar-foreground: theme(colors.slate.50);
  --sidebar-primary: theme(colors.orange.500);
  --sidebar-primary-foreground: theme(colors.slate.950);
  --sidebar-accent: theme(colors.gray.200);
  --sidebar-accent-foreground: theme(colors.slate.950);
  --sidebar-border: theme(colors.slate.800);
  --sidebar-ring: theme(colors.orange.500);

  --success: theme(colors.green.500);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --color-success: var(--success);
}

@theme inline {
  --color-primary-50: theme(colors.orange.50);
  --color-primary-100: theme(colors.orange.100);
  --color-primary-200: theme(colors.orange.200);
  --color-primary-300: theme(colors.orange.300);
  --color-primary-400: theme(colors.orange.400);
  --color-primary-500: theme(colors.orange.500);
  --color-primary-600: theme(colors.orange.600);
  --color-primary-700: theme(colors.orange.700);
  --color-primary-800: theme(colors.orange.800);
  --color-primary-900: theme(colors.orange.900);
  --color-primary-950: theme(colors.orange.950);

  --color-secondary-50: theme(colors.teal.50);
  --color-secondary-100: theme(colors.teal.100);
  --color-secondary-200: theme(colors.teal.200);
  --color-secondary-300: theme(colors.teal.300);
  --color-secondary-400: theme(colors.teal.400);
  --color-secondary-500: theme(colors.teal.500);
  --color-secondary-600: theme(colors.teal.600);
  --color-secondary-700: theme(colors.teal.700);
  --color-secondary-800: theme(colors.teal.800);
  --color-secondary-900: theme(colors.teal.900);
  --color-secondary-950: theme(colors.teal.950);

  --color-destructive-50: theme(colors.rose.50);
  --color-destructive-100: theme(colors.rose.100);
  --color-destructive-200: theme(colors.rose.200);
  --color-destructive-300: theme(colors.rose.300);
  --color-destructive-400: theme(colors.rose.400);
  --color-destructive-500: theme(colors.rose.500);
  --color-destructive-600: theme(colors.rose.600);
  --color-destructive-700: theme(colors.rose.700);
  --color-destructive-800: theme(colors.rose.800);
  --color-destructive-900: theme(colors.rose.900);
  --color-destructive-950: theme(colors.rose.950);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  #credential_picker_container {
    top: 80px !important;
  }
}
